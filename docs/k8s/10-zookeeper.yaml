# Zookeeper Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zookeeper
  namespace: cv-system
  labels:
    app: zookeeper
    tier: infrastructure
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
        tier: infrastructure
    spec:
      serviceAccountName: cv-system-sa
      containers:
      - name: zookeeper
        image: confluentinc/cp-zookeeper:7.4.0
        ports:
        - containerPort: 2181
          name: client
        - containerPort: 2888
          name: follower
        - containerPort: 3888
          name: election
        env:
        - name: ZOOKEEPER_CLIENT_PORT
          value: "2181"
        - name: ZOOKEEPER_TICK_TIME
          value: "2000"
        - name: ZOOKEEPER_SYNC_LIMIT
          value: "5"
        - name: ZO<PERSON>EEPER_INIT_LIMIT
          value: "10"
        - name: ZOOKEEPER_MAX_CLIENT_CNXNS
          value: "60"
        - name: ZOOKEEPER_AUTOPURGE_SNAP_RETAIN_COUNT
          value: "3"
        - name: ZO<PERSON>EEPER_AUTOPURGE_PURGE_INTERVAL
          value: "24"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: zookeeper-data
          mountPath: /var/lib/zookeeper/data
        - name: zookeeper-logs
          mountPath: /var/lib/zookeeper/log
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "echo ruok | nc localhost 2181 | grep imok"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "echo ruok | nc localhost 2181 | grep imok"
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: zookeeper-data
        emptyDir: {}
      - name: zookeeper-logs
        emptyDir: {}
      restartPolicy: Always
---
# Zookeeper Service
apiVersion: v1
kind: Service
metadata:
  name: zookeeper
  namespace: cv-system
  labels:
    app: zookeeper
    tier: infrastructure
spec:
  type: ClusterIP
  ports:
  - port: 2181
    targetPort: 2181
    protocol: TCP
    name: client
  - port: 2888
    targetPort: 2888
    protocol: TCP
    name: follower
  - port: 3888
    targetPort: 3888
    protocol: TCP
    name: election
  selector:
    app: zookeeper
---
# Zookeeper Headless Service (用于集群发现)
apiVersion: v1
kind: Service
metadata:
  name: zookeeper-headless
  namespace: cv-system
  labels:
    app: zookeeper
    tier: infrastructure
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 2181
    targetPort: 2181
    protocol: TCP
    name: client
  - port: 2888
    targetPort: 2888
    protocol: TCP
    name: follower
  - port: 3888
    targetPort: 3888
    protocol: TCP
    name: election
  selector:
    app: zookeeper
