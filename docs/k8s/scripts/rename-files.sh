#!/bin/bash

# 重命名 K8s YAML 文件脚本
# 将文件按照正确的部署顺序重新编号

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}重命名 K8s YAML 文件...${NC}"

# 进入 k8s 目录
cd "$(dirname "$0")/.."

# 定义文件映射 (旧文件名 -> 新文件名)
declare -A file_mapping=(
    ["00-namespace.yaml"]="01-namespace.yaml"
    ["01-storage.yaml"]="02-storage.yaml"
    ["02-configmaps.yaml"]="03-configmaps.yaml"
    ["03-secrets.yaml"]="04-secrets.yaml"
    ["10-zookeeper.yaml"]="05-zookeeper.yaml"
    ["11-kafka.yaml"]="06-kafka.yaml"
    ["12-mongodb.yaml"]="07-mongodb.yaml"
    ["20-scheduler.yaml"]="08-scheduler.yaml"
    ["21-inference.yaml"]="09-inference.yaml"
    ["22-rtsp-server.yaml"]="10-rtsp-server.yaml"
    ["23-inference-mock.yaml"]="11-inference-mock.yaml"
    ["30-ingress.yaml"]="12-ingress.yaml"
    ["40-monitoring.yaml"]="13-monitoring.yaml"
)

# 重命名文件
for old_file in "${!file_mapping[@]}"; do
    new_file="${file_mapping[$old_file]}"
    
    if [ -f "$old_file" ]; then
        echo "重命名: $old_file -> $new_file"
        mv "$old_file" "$new_file"
    else
        echo "文件不存在: $old_file"
    fi
done

echo -e "${GREEN}文件重命名完成！${NC}"

# 显示新的文件列表
echo
echo "新的文件列表:"
ls -1 [0-9][0-9]-*.yaml | sort

echo
echo "部署顺序:"
echo "1. 01-namespace.yaml     # 命名空间和权限"
echo "2. 02-storage.yaml       # 持久化存储"
echo "3. 03-configmaps.yaml    # 配置映射"
echo "4. 04-secrets.yaml       # 密钥配置"
echo "5. 05-zookeeper.yaml     # Zookeeper 部署"
echo "6. 06-kafka.yaml         # Kafka 部署"
echo "7. 07-mongodb.yaml       # MongoDB 部署"
echo "8. 08-scheduler.yaml     # Scheduler 部署"
echo "9. 09-inference.yaml     # Inference 部署"
echo "10. 10-rtsp-server.yaml  # RTSP Server 部署"
echo "11. 11-inference-mock.yaml # Inference Mock 部署"
echo "12. 12-ingress.yaml      # Ingress 配置"
echo "13. 13-monitoring.yaml   # 监控配置"
