# StorageClass 配置
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: cv-system-storage
  namespace: cv-system
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Retain
---
# MongoDB 持久化存储
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mongodb-pv
  labels:
    app: mongodb
spec:
  capacity:
    storage: 20Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: cv-system-storage
  hostPath:
    path: /data/cv-system/mongodb
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongodb-pvc
  namespace: cv-system
  labels:
    app: mongodb
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: cv-system-storage
---
# Kafka 持久化存储
apiVersion: v1
kind: PersistentVolume
metadata:
  name: kafka-pv
  labels:
    app: kafka
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: cv-system-storage
  hostPath:
    path: /data/cv-system/kafka
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kafka-pvc
  namespace: cv-system
  labels:
    app: kafka
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: cv-system-storage
---
# Inference 模型存储
apiVersion: v1
kind: PersistentVolume
metadata:
  name: inference-models-pv
  labels:
    app: inference
    type: models
spec:
  capacity:
    storage: 50Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: cv-system-storage
  hostPath:
    path: /data/cv-system/models
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: inference-models-pvc
  namespace: cv-system
  labels:
    app: inference
    type: models
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: cv-system-storage
---
# RTSP 视频存储
apiVersion: v1
kind: PersistentVolume
metadata:
  name: rtsp-video-pv
  labels:
    app: rtsp-server
    type: video
spec:
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: cv-system-storage
  hostPath:
    path: /data/cv-system/video
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: rtsp-video-pvc
  namespace: cv-system
  labels:
    app: rtsp-server
    type: video
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: cv-system-storage
---
# Inference Mock 截图存储
apiVersion: v1
kind: PersistentVolume
metadata:
  name: inference-mock-screenshots-pv
  labels:
    app: inference-mock
    type: screenshots
spec:
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: cv-system-storage
  hostPath:
    path: /data/cv-system/screenshots
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: inference-mock-screenshots-pvc
  namespace: cv-system
  labels:
    app: inference-mock
    type: screenshots
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: cv-system-storage
