#!/bin/bash

# 快速重命名 K8s YAML 文件脚本
# 简化版本，直接执行重命名操作

set -e

echo "🔄 快速重命名 K8s YAML 文件"
echo "=========================="

# 进入 k8s 目录
cd "$(dirname "$0")/.."

echo "当前目录: $(pwd)"

# 检查是否有需要重命名的文件
old_files=(
    "00-namespace.yaml"
    "20-scheduler.yaml"
    "21-inference.yaml"
    "22-rtsp-server.yaml"
    "23-inference-mock.yaml"
    "30-ingress.yaml"
    "40-monitoring.yaml"
)

found_old_files=()
for file in "${old_files[@]}"; do
    if [ -f "$file" ]; then
        found_old_files+=("$file")
    fi
done

if [ ${#found_old_files[@]} -eq 0 ]; then
    echo "✅ 未找到需要重命名的旧文件"
    echo "当前文件列表:"
    ls -1 [0-9][0-9]-*.yaml 2>/dev/null || echo "  (无 YAML 文件)"
    exit 0
fi

echo "发现需要重命名的文件:"
for file in "${found_old_files[@]}"; do
    echo "  📄 $file"
done

echo

# 执行重命名
echo "开始重命名..."

# 使用临时后缀避免冲突
temp_suffix=".tmp$$"

# 第一步：添加临时后缀
[ -f "00-namespace.yaml" ] && mv "00-namespace.yaml" "01-namespace.yaml$temp_suffix"
[ -f "20-scheduler.yaml" ] && mv "20-scheduler.yaml" "08-scheduler.yaml$temp_suffix"
[ -f "21-inference.yaml" ] && mv "21-inference.yaml" "09-inference.yaml$temp_suffix"
[ -f "22-rtsp-server.yaml" ] && mv "22-rtsp-server.yaml" "10-rtsp-server.yaml$temp_suffix"
[ -f "23-inference-mock.yaml" ] && mv "23-inference-mock.yaml" "11-inference-mock.yaml$temp_suffix"
[ -f "30-ingress.yaml" ] && mv "30-ingress.yaml" "12-ingress.yaml$temp_suffix"
[ -f "40-monitoring.yaml" ] && mv "40-monitoring.yaml" "13-monitoring.yaml$temp_suffix"

# 第二步：移除临时后缀
for file in *"$temp_suffix"; do
    if [ -f "$file" ]; then
        new_name="${file%$temp_suffix}"
        mv "$file" "$new_name"
        echo "✅ 重命名: $(basename "$file" "$temp_suffix") → $new_name"
    fi
done

echo
echo "🎉 重命名完成！"

echo
echo "当前文件列表:"
ls -1 [0-9][0-9]-*.yaml 2>/dev/null | sort | while read -r file; do
    echo "  📄 $file"
done

echo
echo "部署顺序:"
echo "  01-namespace.yaml      → 命名空间和权限"
echo "  02-storage.yaml        → 持久化存储"
echo "  03-configmaps.yaml     → 配置映射"
echo "  04-secrets.yaml        → 密钥配置"
echo "  05-zookeeper.yaml      → Zookeeper"
echo "  06-kafka.yaml          → Kafka"
echo "  07-mongodb.yaml        → MongoDB"
echo "  08-scheduler.yaml      → Scheduler"
echo "  09-inference.yaml      → Inference"
echo "  10-rtsp-server.yaml    → RTSP Server"
echo "  11-inference-mock.yaml → Inference Mock"
echo "  12-ingress.yaml        → Ingress"
echo "  13-monitoring.yaml     → Monitoring"

echo
echo "下一步:"
echo "  ./scripts/verify-files.sh    # 验证文件"
echo "  ./scripts/deploy.sh          # 部署系统"
