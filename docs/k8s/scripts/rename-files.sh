#!/bin/bash

# 重命名 K8s YAML 文件脚本
# 将文件按照正确的部署顺序重新编号

set -e

# 检查 bash 版本 (需要 4.0+ 支持关联数组)
if [ "${BASH_VERSION%%.*}" -lt 4 ]; then
    echo "错误: 需要 bash 4.0 或更高版本来支持关联数组"
    echo "当前版本: $BASH_VERSION"
    echo "请使用 quick-rename.sh 作为替代方案"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔄 重命名 K8s YAML 文件"
echo "======================"

# 进入 k8s 目录
cd "$(dirname "$0")/.."

# 检查当前目录
log_info "当前目录: $(pwd)"

# 显示当前文件
log_info "当前文件列表:"
ls -1 [0-9][0-9]-*.yaml 2>/dev/null || log_warning "未找到任何 YAML 文件"

echo

# 定义文件映射 (旧文件名 -> 新文件名)
declare -A file_mapping=(
    ["00-namespace.yaml"]="01-namespace.yaml"
    ["01-storage.yaml"]="02-storage.yaml"
    ["02-configmaps.yaml"]="03-configmaps.yaml"
    ["03-secrets.yaml"]="04-secrets.yaml"
    ["10-zookeeper.yaml"]="05-zookeeper.yaml"
    ["11-kafka.yaml"]="06-kafka.yaml"
    ["12-mongodb.yaml"]="07-mongodb.yaml"
    ["20-scheduler.yaml"]="08-scheduler.yaml"
    ["21-inference.yaml"]="09-inference.yaml"
    ["22-rtsp-server.yaml"]="10-rtsp-server.yaml"
    ["23-inference-mock.yaml"]="11-inference-mock.yaml"
    ["30-ingress.yaml"]="12-ingress.yaml"
    ["40-monitoring.yaml"]="13-monitoring.yaml"
)

# 确认操作
log_warning "此操作将重命名以下文件:"
found_files=0
for old_file in "${!file_mapping[@]}"; do
    new_file="${file_mapping[$old_file]}"
    if [ -f "$old_file" ]; then
        echo "  $old_file → $new_file"
        found_files=$((found_files + 1))
    fi
done

if [ $found_files -eq 0 ]; then
    log_info "未找到需要重命名的文件"
    echo "当前文件列表:"
    ls -1 [0-9][0-9]-*.yaml 2>/dev/null | while read -r file; do
        echo "  📄 $file"
    done
    exit 0
fi

echo
read -p "确定要继续吗? (y/N): " -r
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "操作已取消"
    exit 0
fi

echo

# 创建临时目录避免冲突
temp_dir="temp_rename_$$"
mkdir -p "$temp_dir"

log_info "开始重命名文件..."

# 第一步：将所有文件移动到临时目录
moved_files=()
for old_file in "${!file_mapping[@]}"; do
    if [ -f "$old_file" ]; then
        mv "$old_file" "$temp_dir/"
        moved_files+=("$old_file")
        log_info "移动到临时目录: $old_file"
    fi
done

# 第二步：从临时目录移动到新文件名
renamed_count=0
for old_file in "${moved_files[@]}"; do
    new_file="${file_mapping[$old_file]}"

    if [ -f "$temp_dir/$old_file" ]; then
        mv "$temp_dir/$old_file" "$new_file"
        log_success "重命名: $old_file → $new_file"
        renamed_count=$((renamed_count + 1))
    else
        log_error "临时文件不存在: $temp_dir/$old_file"
    fi
done

# 清理临时目录
rmdir "$temp_dir" 2>/dev/null || log_warning "临时目录不为空: $temp_dir"

echo
log_success "文件重命名完成！共重命名 $renamed_count 个文件"

# 显示新的文件列表
echo
log_info "新的文件列表:"
if ls -1 [0-9][0-9]-*.yaml &>/dev/null; then
    ls -1 [0-9][0-9]-*.yaml | sort | while read -r file; do
        echo "  ✅ $file"
    done
else
    log_warning "未找到重命名后的文件"
fi

echo
log_info "正确的部署顺序:"
echo "  01. 01-namespace.yaml      # 命名空间和权限"
echo "  02. 02-storage.yaml        # 持久化存储"
echo "  03. 03-configmaps.yaml     # 配置映射"
echo "  04. 04-secrets.yaml        # 密钥配置"
echo "  05. 05-zookeeper.yaml      # Zookeeper 部署"
echo "  06. 06-kafka.yaml          # Kafka 部署"
echo "  07. 07-mongodb.yaml        # MongoDB 部署"
echo "  08. 08-scheduler.yaml      # Scheduler 部署"
echo "  09. 09-inference.yaml      # Inference 部署"
echo "  10. 10-rtsp-server.yaml    # RTSP Server 部署"
echo "  11. 11-inference-mock.yaml # Inference Mock 部署"
echo "  12. 12-ingress.yaml        # Ingress 配置"
echo "  13. 13-monitoring.yaml     # 监控配置"

echo
log_info "下一步操作:"
echo "  1. 验证文件: ./scripts/verify-files.sh"
echo "  2. 构建镜像: ./scripts/build-images.sh"
echo "  3. 部署系统: ./scripts/deploy.sh"
echo "  4. 健康检查: ./scripts/health-check.sh"

echo
log_success "🎉 重命名完成！现在可以按正确顺序部署了。"
