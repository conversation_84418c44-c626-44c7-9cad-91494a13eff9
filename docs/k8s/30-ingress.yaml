# Ingress Controller 配置 (使用 NGINX Ingress)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cv-system-ingress
  namespace: cv-system
  labels:
    app: cv-system
    tier: ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    # 启用 CORS
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
spec:
  rules:
  # Scheduler API 路由
  - host: scheduler.cv-system.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: scheduler
            port:
              number: 8080
  
  # Inference API 路由
  - host: inference.cv-system.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: inference
            port:
              number: 9001
  
  # Inference Mock API 路由
  - host: inference-mock.cv-system.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: inference-mock
            port:
              number: 8081
  
  # RTSP Server API 路由
  - host: rtsp.cv-system.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rtsp-server
            port:
              number: 8889
  
  # 统一入口路由
  - host: cv-system.local
    http:
      paths:
      # Scheduler 路由
      - path: /scheduler(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: scheduler
            port:
              number: 8080
      
      # Inference 路由
      - path: /inference(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: inference
            port:
              number: 9001
      
      # Inference Mock 路由
      - path: /inference-mock(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: inference-mock
            port:
              number: 8081
      
      # RTSP API 路由
      - path: /rtsp(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: rtsp-server
            port:
              number: 8889
      
      # 默认路由到 Scheduler
      - path: /
        pathType: Prefix
        backend:
          service:
            name: scheduler
            port:
              number: 8080
---
# TLS Ingress 配置 (HTTPS)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cv-system-ingress-tls
  namespace: cv-system
  labels:
    app: cv-system
    tier: ingress
    type: tls
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"  # 如果使用 cert-manager
spec:
  tls:
  - hosts:
    - cv-system.example.com
    - scheduler.cv-system.example.com
    - inference.cv-system.example.com
    secretName: cv-system-tls
  rules:
  - host: cv-system.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: scheduler
            port:
              number: 8080
  - host: scheduler.cv-system.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: scheduler
            port:
              number: 8080
  - host: inference.cv-system.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: inference
            port:
              number: 9001
---
# 网络策略 - 限制访问
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cv-system-network-policy
  namespace: cv-system
  labels:
    app: cv-system
    type: security
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 允许来自 Ingress Controller 的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  # 允许命名空间内部通信
  - from:
    - namespaceSelector:
        matchLabels:
          name: cv-system
  # 允许来自监控系统的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080  # Scheduler metrics
    - protocol: TCP
      port: 9001  # Inference metrics
    - protocol: TCP
      port: 8081  # Inference Mock metrics
  egress:
  # 允许 DNS 解析
  - to: []
    ports:
    - protocol: UDP
      port: 53
  # 允许访问外部服务 (如 Triton Server, S3)
  - to: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 8000   # Triton Server
    - protocol: TCP
      port: 38888  # S3 Storage
  # 允许命名空间内部通信
  - to:
    - namespaceSelector:
        matchLabels:
          name: cv-system
---
# 服务发现 - EndpointSlice
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: cv-system-endpoints
  namespace: cv-system
  labels:
    kubernetes.io/service-name: cv-system
addressType: IPv4
ports:
- name: scheduler
  port: 8080
  protocol: TCP
- name: inference
  port: 9001
  protocol: TCP
- name: inference-mock
  port: 8081
  protocol: TCP
endpoints:
- addresses:
  - "***********"  # 示例 IP，实际会自动分配
  conditions:
    ready: true
  targetRef:
    kind: Pod
    name: scheduler-xxx
    namespace: cv-system
