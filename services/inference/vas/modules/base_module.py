import logging
import queue
import time
import yaml
import json
import uuid
import multiprocessing as mp
import numpy as np
import cv2
import tempfile
import os
from enum import Enum
from typing import Optional
from datetime import datetime
from dataclasses import dataclass, field
from vas.common.utils import get_exception_traceback
from vas.common.triton_client_wrapper import TritonClientWrapper
from vas.common.plasma_client_wrapper import PlasmaClientWrapper
from vas.common.s3_storage import upload_event_image
from vas.common.kafka_client import get_kafka_producer, push_event_to_kafka
from vas.entrypoints.task_interface import *
from vas.entrypoints.event_interface import *

DSL_PIPELINE_MODULE = "dsl_pipeline_module"
VIDEO_CAP_MODULE = "video_cap_module"
CLASSIFICATION_MODULE = "classification_module"
OWL_DETECTOR_MODULE = "owl_detector_module"
YOLOV8_DETECTOR_MODULE = "yolov8_detector_module"
BYTE_TRACKER_MODULE = "byte_tracker_module"
TRIPWIRE_INTRUSION_MODULE = "tripwire_intrusion_module"
ZONE_INTRUSION_MODULE = "zone_intrusion_module"


def push_kafka(event: AtomicEventInstance, key: str = None, topic: str = None) -> bool:
    """
    推送事件实例到Kafka

    Args:
        event: 原子事件实例
        key: 消息键，如果为None则使用事件ID

    Returns:
        推送成功返回True，失败返回False
    """
    try:
        return push_event_to_kafka(event, key, topic)
    except Exception as e:
        # 记录错误但不抛出异常，避免影响主流程
        print(f"推送事件到Kafka失败: {e}")
        return False


def upload_image(image_bytes: bytes, task_id: str = None, event_id: str = None) -> str:
    """
    上传图片到S3存储

    Args:
        image_bytes: 图片字节数据（通常是cv2.imencode的结果）
        task_id: 任务ID，如果为None则尝试从当前上下文获取
        event_id: 事件ID，如果为None则自动生成

    Returns:
        S3存储的URL，失败时返回空字符串
    """
    try:
        # 如果没有提供event_id，生成一个
        if event_id is None:
            event_id = f"event_{uuid.uuid4().hex[:8]}"

        # 如果没有提供task_id，使用默认值
        if task_id is None:
            task_id = "unknown_task"

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
            temp_file.write(image_bytes)
            temp_file_path = temp_file.name

        try:
            # 上传到S3
            s3_url = upload_event_image(
                local_file_path=temp_file_path,
                task_id=task_id,
                event_id=event_id,
                timestamp=datetime.now()
            )

            # 返回URL，如果上传失败则返回空字符串
            return s3_url if s3_url else ""

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    except Exception as e:
        # 记录错误但不抛出异常，避免影响主流程
        print(f"上传图片失败: {e}")
        return ""


class MessageType(str, Enum):
    NONE_MESSAGE_TYPE = "none"
    EXECUTE_TASK_MESSAGE_TYPE = "execute_task"
    ADD_TASK_MESSAGE_TYPE = "add_task"
    DELETE_TASK_MESSAGE_TYPE = "delete_task"


@dataclass
class MetaInfo:
    frame_id: int = -1
    timestamp: str = ""
    upload_url: str = None


@dataclass
class ShmDataInfo:
    # object shape
    shape: list[int] = field(default_factory=list)
    # object type
    type: str = "float64"
    # object shm id
    id: bytes = b''


@dataclass
class FrameInfo:
    # frame meta ()locate in shm
    frame: ShmDataInfo = field(default_factory=ShmDataInfo)
    # bboxes info locate in shm, [num_det, 6], and 6: [x, y, w, h, score, class_id]
    bboxes: ShmDataInfo = field(default_factory=ShmDataInfo)
    # tracker object info locate in shm, [num_det, 7], represent [x, y, w, h, score, class_id, track_id]
    track_objs: ShmDataInfo = field(default_factory=ShmDataInfo)


@dataclass
class MessageInfo:
    # message type
    message_type: MessageType = MessageType.NONE_MESSAGE_TYPE

    # task info
    task_info: Optional[SimplifiedAtomicTask] = None

    # frame meta info
    meta_info: MetaInfo = field(default_factory=MetaInfo)

    # frame info, contain frame, bboxes, tracker ids
    frame_info: FrameInfo = field(default_factory=FrameInfo)

    # 标记位：是否跳过处理直接传递给下一个节点
    # True: 跳过处理，直接传递给下一个节点
    # False: 正常处理
    skip_processing: bool = False

    # 告警信息字典，保存分类模块告警信息，并在最后一个模块进行上报
    # key: 算法模块order
    # value: 对应告警结果列表
    alert_info: dict = field(default_factory=dict)


@dataclass
class ClassificationDetectionAttr:
    score: float
    type: str
    label: str
    obj_id: int | str = ""  # 支持整数trackId或字符串


@dataclass
class DetectionClassificationAlert:
    alert: bool
    bbox: list[int]
    classifications: list[ClassificationDetectionAttr]


class BaseModule:

    def __init__(self, module_info, cmd_args, queues, logger, task_info: None | SimplifiedAtomicTask = None):

        self.module_type = module_info["type"]
        self.module_order = module_info["order"]
        self.name = self.module_type + "_" + str(self.module_order)
        self.cmd_args = cmd_args
        self.queues = queues
        self.logger = logger
        self.work_mode = "trigger"  # or "loop"

        # update numpy log format
        np.set_printoptions(precision=4, suppress=True, linewidth=120, formatter={'float': '{:0.4f}'.format})

        # init task info
        self.task_info = None
        if task_info is not None:
            self.task_info = task_info

        # statistic time
        self.statistic_time = 0
        self.statistic_count = 1

        # 统计信息
        self.processed_frames = 0
        self.last_stats_time = time.time()

        # load config from file
        with open(self.cmd_args.config_path, 'r') as file:
            self.config = yaml.safe_load(file)

        # update work mode
        if "work_mode" in self.config[self.module_type]:
            self.work_mode = self.config[self.module_type]["work_mode"]

        # init from triton client
        triton_config = self.config["triton_server"]
        ip_addr = triton_config["ip"]
        grpc_port = triton_config["port"]
        self.infer_client = TritonClientWrapper(ip_addr, grpc_port)
        server_ready = self.infer_client.is_server_ready()
        self.logger.info(f"infer server {ip_addr}:{grpc_port} status is {server_ready}")

        # init from plasma client
        self.mock_plasma = True
        plasma_config = self.config["plasma_server"]
        plasma_path = plasma_config["path"]
        self.shm_client = PlasmaClientWrapper(plasma_path, self.mock_plasma)

        # next module
        self.next_module = self.get_next_module(self.module_order + 1, self.task_info)


    def get_module_type(self, algorithm: Algorithm):

        ALGORITHM_TYPE_TO_MODULE = {
            "CLASSIFICATION": CLASSIFICATION_MODULE,
            "TRACKING": BYTE_TRACKER_MODULE,
            "DETECTION": YOLOV8_DETECTOR_MODULE,  # 默认YOLO检测
            "OWL_DETECTION": OWL_DETECTOR_MODULE,
            "RULE": None,  # 规则类型需要特殊处理
            "PIPELINE": DSL_PIPELINE_MODULE
        }
        algo_type = algorithm.algorithmType
        if algo_type == "DETECTION":
            # OWL/OVIT检测：通过算法ID区分
            if ("owl" in algorithm.algorithmId.lower() or "owl" in algorithm.algorithmName.lower() or
                "ovit" in algorithm.algorithmId.lower() or "ovit" in algorithm.algorithmName.lower()):
                module_type = OWL_DETECTOR_MODULE
            # YOLO检测：统一使用"detection"算法ID，内部通过detection_type配置区分人车非
            else:
                module_type = YOLOV8_DETECTOR_MODULE
        elif algo_type == "RULE":
            # 根据算法ID确定具体的规则模块
            if ("tripwire" in algorithm.algorithmId.lower() or "tripwire" in algorithm.algorithmName.lower()):
                module_type = TRIPWIRE_INTRUSION_MODULE
            elif ("zone" in algorithm.algorithmId.lower() or "zone" in algorithm.algorithmName.lower() or
                    "intrusion" in algorithm.algorithmId.lower() or "intrusion" in algorithm.algorithmName.lower()):
                module_type = ZONE_INTRUSION_MODULE
            else:
                # 默认使用区域入侵模块
                module_type = ZONE_INTRUSION_MODULE
        else:
            module_type = ALGORITHM_TYPE_TO_MODULE.get(algo_type)

        return module_type


    def get_next_module(self, next_order, task_info: None | SimplifiedAtomicTask):
        if task_info == None:
            return None

        next_module = None
        algorithm_chain = task_info.algorithmOrchestration.algorithmChain
        for algorithm in algorithm_chain:
            algo_order = algorithm.order
            if algo_order != next_order:
                continue
            next_module = self.get_module_type(algorithm)

        if next_module is not None:
            next_module = next_module + "_" + str(next_order)
        return next_module


    def get_module_algo_config(self, message: None | MessageInfo = None):
        if message is not None:
            task_info = message.task_info
        else:
            task_info = self.task_info

        if task_info is None:
            return None

        for algo_config in task_info.algorithmOrchestration.algorithmChain:
            module_type = self.get_module_type(algo_config)
            module_order = algo_config.order
            if module_type == self.module_type and module_order == self.module_order:
                return algo_config
        return None


    def upload_image(self, frame_bytes: bytes, task_id: str = None, event_id: str = None) -> str:
        """
        上传图片到S3存储（实例方法）

        Args:
            frame_bytes: 图片字节数据
            task_id: 任务ID，如果为None则尝试从当前任务信息获取
            event_id: 事件ID，如果为None则自动生成

        Returns:
            S3存储的URL，失败时返回空字符串
        """
        # 如果没有提供task_id，尝试从当前任务信息获取
        if task_id is None:
            task_id = "unknown"

        # 调用全局的upload_image函数
        return upload_image(frame_bytes, task_id=task_id, event_id=event_id)


    def push_kafka(self, alert_event: AtomicEventInstance, key: str = None) -> bool:
        """
        推送事件实例到Kafka（实例方法）

        Args:
            alert_event: 原子事件实例
            key: 消息键，如果为None则使用事件ID

        Returns:
            推送成功返回True，失败返回False
        """
        # 调用全局的push_kafka函数
        return push_kafka(alert_event, key)


    def generate_alert_event(self, frame: np.ndarray,
                                frame_meta: MetaInfo,
                                algo_config: Algorithm,
                                task_info: SimplifiedAtomicTask, 
                                alert_res: list[DetectionClassificationAlert],
                                topic: str = None):
        uid = uuid.uuid4()
        uid_str = str(uid)
        unique_id = uid_str

        # upload image
        if frame_meta.upload_url is not None and "" != frame_meta.upload_url:
            url = frame_meta.upload_url
        else:
            retval, encode_buf = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 95])
            if retval:
                task_id = task_info.taskId
                url = upload_image(encode_buf.tobytes(), task_id=task_id, event_id=unique_id)
            else:
                url = ""
            frame_meta.upload_url = url

        # device info
        device_name = task_info.device.deviceName
        device_id = task_info.device.deviceId

        # task info
        task_id = task_info.taskId
        event_type_id = task_info.taskMeta.eventTypeId
        orchestration_id = task_info.algorithmOrchestration.orchestrationId
        orchestration_type = task_info.algorithmOrchestration.orchestrationType
        task_level = task_info.taskMeta.taskLevel

        # frame info
        frame_id = frame_meta.frame_id
        # 将字符串时间戳转换为毫秒时间戳
        if frame_meta.timestamp:
            try:
                # 尝试解析时间戳字符串，格式如 "2025-07-27 21:54:22.288"
                dt = datetime.strptime(frame_meta.timestamp, "%Y-%m-%d %H:%M:%S.%f")
                frame_timestamp = int(dt.timestamp() * 1000)
            except ValueError:
                # 如果解析失败，使用当前时间
                frame_timestamp = int(datetime.now().timestamp() * 1000)
        else:
            # 如果没有时间戳，使用当前时间
            frame_timestamp = int(datetime.now().timestamp() * 1000)

        task_attr = TaskInfo(taskId=task_id, 
                            eventTypeId=event_type_id,
                            orchestrationId=orchestration_id,
                            orchestrationType=orchestration_type,
                            taskLevel=task_level,
                            deviceName=device_name,
                            frameId=frame_id)

        entities = []
        algo_id = algo_config.algorithmId
        for alert_item in alert_res:
            # 从分类结果中获取第一个分类的信息
            if alert_item.classifications and len(alert_item.classifications) > 0:
                first_classification = alert_item.classifications[0]
                # 确保trackId是数字字符串
                if first_classification.obj_id:
                    # 如果obj_id存在且不为空，直接转换为字符串（保持数字格式）
                    track_id = str(first_classification.obj_id)
                else:
                    # 如果没有obj_id，生成一个数字ID而不是带前缀的ID
                    track_id = str(abs(hash(uuid.uuid4().hex)) % 1000000)  # 生成6位数字ID
                obj_label = first_classification.label
            else:
                # 如果没有分类结果，生成一个数字ID
                track_id = str(abs(hash(uuid.uuid4().hex)) % 1000000)  # 生成6位数字ID
                obj_label = ""

            bbox = BoundingBox(x=alert_item.bbox[0],
                                y=alert_item.bbox[1],
                                width=alert_item.bbox[2],
                                height=alert_item.bbox[3])
            entities.append(EntityInstance(entityInstanceId=track_id,
                                            entityType=obj_label,
                                            algorithmId=algo_id,
                                            boundingBox=bbox))

        # 创建事件实例
        alert_event = AtomicEventInstance(atomicEventInstanceId=unique_id,
                            eventTypeId=event_type_id,
                            taskId=task_id,
                            deviceId=device_id,
                            timestamp=frame_timestamp,
                            imageUri=url,
                            entities=entities,
                            taskInfo=task_attr)

        # 自动推送到Kafka
        try:
            push_success = push_kafka(alert_event, key=task_id, topic=topic)
            if push_success:
                self.logger.info(f"事件已推送到Kafka: event_id={unique_id}, task_id={task_id}")
            else:
                self.logger.warning(f"事件推送到Kafka失败: event_id={unique_id}, task_id={task_id}")
        except Exception as e:
            self.logger.error(f"推送事件到Kafka异常: {e}, event_id={unique_id}")

        return alert_event


    def check_model_ready(self, model_name):
        model_ready = self.infer_client.is_model_ready(model_name)
        if isinstance(model_ready, str) or \
            (isinstance(model_ready, bool) and not model_ready):
            self.logger.warning(f"try to load model:{model_name}")
            loaded_status = self.infer_client.load_model(model_name)
            if isinstance(loaded_status, str):
                assert False, f"model: {model_name} is unavailable, load status: {loaded_status}"
            model_ready = True
        return model_ready


    def load_task_from_config(self, config_file):
        with open(config_file, 'r', encoding='utf-8') as file:
            task_json = json.load(file)
        task_info = SimplifiedAtomicTask.parse_obj(task_json)
        return task_info


    def get_from_shm(self, shm_info: ShmDataInfo):
        obj_id = shm_info.id
        obj_shape = shm_info.shape
        obj_type = shm_info.type
        obj_bytes = self.shm_client.get_item(obj_id)
        np_obj = np.frombuffer(obj_bytes, dtype=np.dtype(obj_type))
        if len(obj_shape):
            np_obj = np_obj.reshape(obj_shape)
        return np_obj


    def put_to_shm(self, np_obj: np.ndarray | None) -> ShmDataInfo:
        obj_info = ShmDataInfo()
        if isinstance(np_obj, np.ndarray) and 0 != np_obj.size:
            obj_shape = np_obj.shape
            obj_type = str(np_obj.dtype)
            obj_info.shape = obj_shape
            obj_info.type = obj_type
            obj_bytes = np_obj.tobytes()
            obj_id = self.shm_client.put_item(obj_bytes)
            obj_info.id = obj_id
        return obj_info


    def delete_from_shm(self, obj_info: ShmDataInfo):
        obj_id = obj_info.id
        if 0 != len(obj_id):
            self.shm_client.delete_item(obj_id)


    def extract_frame_info(self, message: MessageInfo):
        frame_info = message.frame_info
        frame = self.get_from_shm(frame_info.frame)
        bboxes = self.get_from_shm(frame_info.bboxes)
        track_objs = self.get_from_shm(frame_info.track_objs)
        return frame, bboxes, track_objs


    def update_frame_info(self, message: MessageInfo, 
                        frame: np.ndarray | None = None, 
                        bboxes: np.ndarray | None = None, 
                        track_objs: np.ndarray | None = None):

        # update frame obj
        frame_info = message.frame_info
        if isinstance(frame, np.ndarray):
            self.delete_from_shm(frame_info.frame)
            frame_info.frame = self.put_to_shm(frame)

        # update bboxes
        if isinstance(bboxes, np.ndarray):
            self.delete_from_shm(frame_info.bboxes)
            frame_info.bboxes = self.put_to_shm(bboxes)

        # update track_objs
        if isinstance(track_objs, np.ndarray):
            self.delete_from_shm(frame_info.track_objs)
            frame_info.track_objs = self.put_to_shm(track_objs)


    def prepare_frame_info(self, frame: np.ndarray | None = None, 
                        bboxes: np.ndarray | None = None, 
                        track_objs: np.ndarray | None = None) -> FrameInfo:
        frame_shm = self.put_to_shm(frame)
        bboxes_shm = self.put_to_shm(bboxes)
        track_objs_shm = self.put_to_shm(track_objs)
        return FrameInfo(frame=frame_shm, bboxes=bboxes_shm, track_objs=track_objs_shm)


    def prepare_meta_info(self, frame_id) -> MetaInfo:
        return MetaInfo(frame_id)


    def prepare_none_message(self) -> MessageInfo:
        return MessageInfo()


    def prepare_execute_task_message(self, frame_info: FrameInfo, 
                                    meta_info: MetaInfo = MetaInfo(), 
                                    task_info: SimplifiedAtomicTask | None = None) -> MessageInfo:
        message_type = MessageType.EXECUTE_TASK_MESSAGE_TYPE
        if task_info is not None:
            message = MessageInfo(message_type, task_info, meta_info, frame_info)
        else:
            message = MessageInfo(message_type=message_type, meta_info=meta_info, frame_info=frame_info)
        return message


    def delete_message(self, message: MessageInfo):
        frame_info = message.frame_info
        self.delete_from_shm(frame_info.frame)
        self.delete_from_shm(frame_info.bboxes)
        self.delete_from_shm(frame_info.track_objs)


    def push_message_to_module(self, message: MessageInfo, module_name: str, block: bool = True):
        if module_name not in self.queues.keys():
            self.logger.warning(f"cannot find {module_name}'s queue.")
        else:
            self.queues[module_name].put(message)


    def process_add_task_message(self, message: MessageInfo):
        self.logger.warning(f"not implement process add task message.")
        time.sleep(0.1)


    def process_delelte_task_message(self, message: MessageInfo):
        self.logger.warning(f"not implement process delete task message.")
        time.sleep(0.1)


    def process_execute_task_message(self, message: MessageInfo):
        self.logger.warning(f"not implement process execute task message.")
        time.sleep(0.1)


    def process_none_message(self, message: MessageInfo):
        self.logger.warning(f"not implement process none message.")
        time.sleep(0.1)


    def log_info(self, format_str: str, *args, **kwargs):
        log_str = format_str.format(*args, **kwargs)
        self.logger.info(log_str)


    def log_warn(self, format_str: str, *args, **kwargs):
        log_str = format_str.format(*args, **kwargs)
        self.logger.warning(log_str)


    def log_error(self, format_str: str, *args, **kwargs):
        log_str = format_str.format(*args, **kwargs)
        self.logger.error(log_str)


    def process_message(self, message: MessageInfo):
        message_type = message.message_type
        if message_type == MessageType.ADD_TASK_MESSAGE_TYPE:
            processed_message = self.process_add_task_message(message)
        elif message_type == MessageType.DELETE_TASK_MESSAGE_TYPE:
            processed_message = self.process_delelte_task_message(message)
        elif message_type == MessageType.EXECUTE_TASK_MESSAGE_TYPE:
            processed_message = self.process_execute_task_message(message)
        elif message_type == MessageType.NONE_MESSAGE_TYPE:
            processed_message = self.process_none_message(message)
        else:
            self.logger.warning(f"current not supported process {message_type} message.")
        return processed_message


    def compute_fps(self, start: float, end: float):
        self.statistic_time += (end - start)
        avg_time = self.statistic_time / self.statistic_count
        process_fps = 1 / avg_time
        self.statistic_count += 1
        if 0 == self.statistic_count % 100:
            self.logger.info(f"process {process_fps} fps")
            self.statistic_time = 0
            self.statistic_count = 1


    def get_module_queue_message(self):
        block = self.work_mode == "trigger"
        try:
            if block:
                message = self.queues[self.name].get()
            else:
                message = self.queues[self.name].get(timeout=0.1)
        except queue.Empty:
            message = self.prepare_none_message()
        return message


    def send_to_next_module(self, message: MessageInfo):
        # 记录消息传递状态
        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"
        skip_status = message.skip_processing if message else False

        if self.next_module is not None:
            self.logger.debug(f"[{self.name}] Frame {frame_id}: 传递消息到下一个模块 {self.next_module}, skip_processing={skip_status}")
            self.push_message_to_module(message, self.next_module)
        else:
            self.logger.debug(f"[{self.name}] Frame {frame_id}: 当前是最后一个节点")
            # 当前节点是最后一个节点，且消息不为空时，推送告警
            if message is not None and not message.skip_processing:
                self.logger.debug(f"[{self.name}] Frame {frame_id}: 满足告警条件 - 消息不为空且未跳过处理，开始推送告警")
                self.handle_final_node_alert(message)
            elif message is None:
                self.logger.debug(f"[{self.name}] Frame {frame_id}: 消息为空，不推送告警")
            elif message.skip_processing:
                self.logger.debug(f"[{self.name}] Frame {frame_id}: 消息被标记跳过处理，不推送告警")


    def handle_final_node_alert(self, message: MessageInfo):
        """
        处理最后一个节点的告警推送逻辑
        当当前节点是最后一个节点，且消息不为空时，推送告警
        """
        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"

        try:
            self.logger.info(f"[{self.name}] Frame {frame_id}: 开始处理最后节点告警逻辑")

            # 检查是否有检测结果需要告警
            frame, bboxes, track_objs = self.extract_frame_info(message)

            self.logger.debug(f"[{self.name}] Frame {frame_id}: 提取帧信息 - frame shape: {frame.shape if frame is not None else 'None'}, "
                            f"bboxes count: {len(bboxes) if bboxes is not None else 0}, "
                            f"track_objs count: {len(track_objs) if track_objs is not None else 0}")

            # 如果有检测结果，生成告警事件
            alert_info = message.alert_info
            if alert_info is not None and len(alert_info) > 0:

                # 检查是否有任务信息，如果没有则跳过告警生成
                if message.task_info is None:
                    self.logger.warning(f"[{self.name}] Frame {frame_id}: ⚠️ 消息中无任务信息，无法生成告警事件")
                    return

                # 依次处理每个算法的告警结果
                for idx, algo_order in enumerate(alert_info):
                    alert_results = alert_info[algo_order]

                    # 获取算法配置
                    algo_config = None
                    if message.task_info.algorithmOrchestration and message.task_info.algorithmOrchestration.algorithmChain:
                        algo_chain = message.task_info.algorithmOrchestration.algorithmChain
                        for algo_idx in range(len(algo_chain)):
                            if algo_chain[algo_idx].order == algo_order:
                                algo_config = algo_chain[algo_idx]

                    if algo_config:
                        # 生成告警事件 - 使用正确的参数
                        alert_event = self.generate_alert_event(
                            frame=frame,
                            frame_meta=message.meta_info,
                            algo_config=algo_config,
                            task_info=message.task_info,
                            alert_res=alert_results
                        )

                        if alert_event:
                            # 提取告警相关的trackId信息
                            track_ids = []
                            if alert_event.entities:
                                track_ids = [entity.entityInstanceId for entity in alert_event.entities]
                            track_ids_str = ",".join(track_ids) if track_ids else "无"
                            self.logger.info(f"🚨 [ALERT] Frame {frame_id}: 告警事件生成成功 | TrackIDs: {track_ids_str} | 事件ID: {alert_event.atomicEventInstanceId}")
                        else:
                            self.logger.warning(f"[{self.name}] Frame {frame_id}: ❌ 告警事件生成失败")
                    else:
                        self.logger.warning(f"[{self.name}] Frame {frame_id}: ⚠️ 无算法配置，无法生成告警事件")
            else:
                self.logger.debug(f"[{self.name}] Frame {frame_id}: ⚠️ 无检测结果，不推送告警")

        except Exception as e:
            traceback_info = get_exception_traceback()
            self.logger.error(f"[{self.name}] Frame {frame_id}: ❌ 最后节点告警处理异常: {e}\n{traceback_info}")


    def event_loop(self):
        while True:
            try:
                start = time.time()
                message = self.get_module_queue_message()
                # 记录接收到的frameId（debug级别）
                frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"
                self.logger.debug(f"[{self.name}] 接收到消息 Frame {frame_id}")

                processed_message = self.process_message(message)

                # 检查frameId是否发生变化（只在变化时警告）
                processed_frame_id = processed_message.meta_info.frame_id if processed_message and processed_message.meta_info else "unknown"
                if frame_id != processed_frame_id:
                    self.logger.warning(f"[{self.name}] ⚠️ Frame ID 发生变化: {frame_id} -> {processed_frame_id}")

                self.send_to_next_module(processed_message)
                self.compute_fps(start, time.time())

                # 统计信息（每60秒输出一次）
                self.processed_frames += 1
                current_time = time.time()
                if current_time - self.last_stats_time >= 60:  # 60秒
                    fps = self.processed_frames / (current_time - self.last_stats_time)
                    self.logger.info(f"📊 [{self.name}] 统计信息: 处理帧数 {self.processed_frames}, 平均FPS {fps:.2f}")
                    self.processed_frames = 0
                    self.last_stats_time = current_time
            except Exception:
                last_traceback = get_exception_traceback()
                self.logger.error(f"error: {last_traceback}")
                break