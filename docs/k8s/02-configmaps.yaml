# Scheduler 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: scheduler-config
  namespace: cv-system
  labels:
    app: scheduler
data:
  application.yml: |
    server:
      port: 8080
    
    spring:
      application:
        name: cv-scheduler
      
      data:
        mongodb:
          uri: ${MONGODB_URI:***********************************************************************}
          database: ${MONGODB_DATABASE:cv_scheduler}
          auto-index-creation: true
    
    scheduler:
      lock:
        type: local
      
      health-check:
        interval: 30000
        timeout: 5000
        enabled: true
        recovery-interval: 60000
      
      strategy:
        default-mode: FILL_FIRST
        enable-dynamic-schedule: true
        rebalance-threshold: 80
      
      service:
        discovery:
          enabled: true
          interval: 60000
    
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: always
    
    logging:
      level:
        com.bohua.scheduler: INFO
        org.springframework.web: INFO
        org.springframework.data.mongodb: INFO
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
---
# Inference 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: inference-config
  namespace: cv-system
  labels:
    app: inference
data:
  config.yaml: |
    service_registry:
      enabled: true
      scheduler:
        url: "http://scheduler:8080"
        registration_retry_interval: 30
        health_check_interval: 60
      service:
        name: "${SERVICE_NAME:-inference-service}"
        max_quota: "${MAX_QUOTA:-10}"
        region: "${REGION:-default}"
        gpu_type: "${GPU_TYPE:-A10}"
        tags: ["production", "gpu-enabled"]
    
    http_server:
      host: "0.0.0.0"
      port: 9001
      debug: false
    
    task_manager:
      max_concurrent_tasks: 10
      task_timeout: 3600
      cleanup_interval: 300
    
    modules:
      dsl_pipeline_module:
        work_mode: "trigger"
        video_decode:
          max_width: 1920
          max_height: 1080
          target_fps: 25
      
      yolov8_detector_module:
        work_mode: "trigger"
        model_name: "yolov8"
        detection_type: "PERSON"
        input_width: 640
        input_height: 640
      
      byte_tracker_module:
        work_mode: "trigger"
        track_thresh: 0.45
        track_buffer: 25
        match_thresh: 0.8
        frame_rate: 25
      
      classification_module:
        work_mode: "trigger"
        text_model:
          model_name: "cnclip_text"
          model_path: "/app/models/AI-ModelScope/chinese-clip-vit-large-patch14"
        visual_model:
          model_name: "cnclip_visual"
          model_path: "/app/models/AI-ModelScope/chinese-clip-vit-large-patch14"
      
      zone_intrusion_module:
        work_mode: "trigger"
        alarm_thresh: 3.0
        re_alarm_thresh: 10.0
    
    triton_server:
      ip: "triton-server"
      port: 8000
    
    kafka:
      bootstrap_servers: ["kafka:9092"]
      topic: "video_analysis_events"
      producer:
        acks: 1
        retries: 3
        batch_size: 16384
        linger_ms: 10
    
    logging:
      level: INFO
      format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
      file:
        path: "logs/inference.log"
        max_size: "100MB"
        backup_count: 10
---
# Inference Mock 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: inference-mock-config
  namespace: cv-system
  labels:
    app: inference-mock
data:
  config.yaml: |
    server:
      host: "0.0.0.0"
      port: 8081
      
    scheduler:
      url: "http://scheduler:8080"
      registration_retry_interval: 30
      health_check_interval: 60
      
    service:
      name: "${SERVICE_NAME:-inference-mock-1}"
      region: "${REGION:-default}"
      gpu_type: "${GPU_TYPE:-A10}"
      max_quota: "${MAX_QUOTA:-10}"
      current_orchestration: "${CURRENT_ORCHESTRATION:-YOLO_TRACKING_CLIP}"
      algorithm_orchestration:
        orchestrationId: "orch-001"
        orchestrationType: "YOLO_TRACKING_CLIP"
        algorithmChain:
          - algorithmId: "yolo-v8"
            algorithmName: "YOLO目标检测"
            algorithmType: "DETECTION"
            order: 1
            required: true
            config:
              confidence_threshold: 0.5
              nms_threshold: 0.4
      
    rtsp:
      connection_timeout: 10
      retry_interval: 5
      max_retries: 3
      frame_buffer_size: 1
      default_server: "rtsp-server:9554"
      enabled: true
      simulation_mode: true
      stream_mapping:
        "camera-001": "rtsp://rtsp-server:9554/knight"
        "camera-002": "rtsp://rtsp-server:9554/test"
        "default": "rtsp://rtsp-server:9554/knight"
      
    screenshot:
      interval: 30
      output_dir: "./screenshots"
      format: "jpg"
      quality: 85
      max_files: 100
      
    kafka:
      bootstrap_servers: ["kafka:9092"]
      topic: "vision-events"
      client_id: "${SERVICE_NAME:-inference-mock-1}"
      acks: 1
      retries: 3
      batch_size: 16384
      linger_ms: 10
    
    logging:
      level: INFO
      format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
---
# RTSP Server 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: rtsp-server-config
  namespace: cv-system
  labels:
    app: rtsp-server
data:
  mediamtx.yml: |
    # MediaMTX配置文件
    logLevel: info
    logDestinations: [stdout]
    logFile: /logs/mediamtx.log
    
    readTimeout: 10s
    writeTimeout: 10s
    writeQueueSize: 1024
    udpMaxPayloadSize: 1472
    
    api: true
    apiAddress: :8889
    
    metrics: true
    metricsAddress: :9998
    
    rtspAddress: :9554
    protocols: [tcp, udp]
    encryption: "no"
    authMethods: [basic]
    
    webrtc: true
    webrtcAddress: :8888
    
    hls: false
    
    pathDefaults:
      record: false
    
    paths:
      ~^.*$:
        source: publisher
        publishUser: ""
        publishPass: ""
        readUser: ""
        readPass: ""
