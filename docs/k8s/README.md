# CV 推理系统 Kubernetes 部署手册

## 📋 概述

本手册提供 CV 推理系统在 Kubernetes 环境下的完整部署方案，包括所有服务组件的 YAML 配置文件和部署步骤。

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        Kubernetes 集群                          │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Namespace: cv-system                     │ │
│  │                                                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │ │
│  │  │ Scheduler   │  │ Inference   │  │ RTSP Server │        │ │
│  │  │   :8080     │  │   :9001     │  │   :9554     │        │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘        │ │
│  │                                                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │ │
│  │  │ MongoDB     │  │   Kafka     │  │Inference-Mock│        │ │
│  │  │   :27017    │  │   :9092     │  │   :8081     │        │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘        │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 📦 服务组件

| 服务 | 镜像 | 端口 | 描述 |
|------|------|------|------|
| **Scheduler** | `cv/scheduler:latest` | 8080 | Java 任务调度器 |
| **Inference** | `cv/inference:latest` | 9001 | Python 推理服务 |
| **Inference-Mock** | `cv/inference-mock:latest` | 8081 | Python 模拟推理服务 |
| **RTSP-Server** | `cv/rtsp-server:latest` | 9554 | 视频流服务器 |
| **MongoDB** | `mongo:6.0` | 27017 | 文档数据库 |
| **Kafka** | `confluentinc/cp-kafka:7.4.0` | 9092 | 消息队列 |
| **Zookeeper** | `confluentinc/cp-zookeeper:7.4.0` | 2181 | Kafka 协调服务 |

## 🚀 快速部署

### 1. 前置条件

```bash
# 确保 Kubernetes 集群可用
kubectl cluster-info

# 确保有足够的资源
kubectl top nodes

# 安装 kubectl 和 helm (可选)
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"

# 确保有 GPU 节点 (如果部署 Inference 服务)
kubectl get nodes -l accelerator=nvidia-tesla-a10
```

### 2. 构建 Docker 镜像

```bash
# 进入脚本目录
cd scripts

# 构建所有镜像
./build-images.sh

# 或者构建特定服务
./build-images.sh --service scheduler
./build-images.sh --service inference-mock

# 推送到镜像仓库 (可选)
./build-images.sh --registry registry.example.com --push
```

### 3. 一键部署

```bash
# 使用部署脚本 (推荐)
./scripts/deploy.sh

# 跳过镜像检查
./scripts/deploy.sh --skip-images

# 跳过监控部署
./scripts/deploy.sh --skip-monitoring
```

### 4. 手动部署 (分步骤)

```bash
# 1. 创建命名空间和权限
kubectl apply -f 01-namespace.yaml

# 2. 部署存储和配置
kubectl apply -f 02-storage.yaml
kubectl apply -f 03-configmaps.yaml
kubectl apply -f 04-secrets.yaml

# 3. 部署基础设施
kubectl apply -f 05-zookeeper.yaml
kubectl apply -f 06-kafka.yaml
kubectl apply -f 07-mongodb.yaml

# 等待基础设施就绪
kubectl wait --for=condition=ready pod -l app=zookeeper -n cv-system --timeout=300s
kubectl wait --for=condition=ready pod -l app=kafka -n cv-system --timeout=300s
kubectl wait --for=condition=ready pod -l app=mongodb -n cv-system --timeout=300s

# 4. 部署应用服务
kubectl apply -f 08-scheduler.yaml
kubectl apply -f 09-inference.yaml      # 需要 GPU 节点
kubectl apply -f 10-rtsp-server.yaml
kubectl apply -f 11-inference-mock.yaml

# 等待服务就绪
kubectl wait --for=condition=ready pod -l app=scheduler -n cv-system --timeout=300s
kubectl wait --for=condition=ready pod -l app=inference-mock -n cv-system --timeout=300s

# 5. 部署网络和监控 (可选)
kubectl apply -f 12-ingress.yaml        # 需要 Ingress Controller
kubectl apply -f 13-monitoring.yaml     # 需要 Prometheus Operator
```

### 5. 验证部署

```bash
# 使用健康检查脚本 (推荐)
./scripts/health-check.sh

# 详细检查
./scripts/health-check.sh --detailed

# 手动检查
kubectl get pods -n cv-system
kubectl get services -n cv-system
kubectl get pvc -n cv-system
```

## 📁 文件结构

```
docs/k8s/
├── README.md                    # 本文件
├── QUICKSTART.md               # 快速开始指南
├── Makefile                    # 便捷操作命令
├── 01-namespace.yaml           # 命名空间和权限
├── 02-storage.yaml             # 持久化存储
├── 03-configmaps.yaml          # 配置映射
├── 04-secrets.yaml             # 密钥配置
├── 05-zookeeper.yaml           # Zookeeper 部署
├── 06-kafka.yaml               # Kafka 部署
├── 07-mongodb.yaml             # MongoDB 部署
├── 08-scheduler.yaml           # Scheduler 部署
├── 09-inference.yaml           # Inference 部署
├── 10-rtsp-server.yaml         # RTSP Server 部署
├── 11-inference-mock.yaml      # Inference Mock 部署
├── 12-ingress.yaml             # Ingress 配置
├── 13-monitoring.yaml          # 监控配置
└── scripts/
    ├── deploy.sh               # 一键部署脚本
    ├── cleanup.sh              # 清理脚本
    ├── health-check.sh         # 健康检查脚本
    └── build-images.sh         # 镜像构建脚本
```

## ⚙️ 配置说明

### 环境变量配置

| 服务 | 环境变量 | 默认值 | 说明 |
|------|----------|--------|------|
| **Scheduler** | `MONGODB_URI` | `***********************************************************************` | MongoDB 连接字符串 |
| **Scheduler** | `SERVER_PORT` | `8080` | 服务端口 |
| **Inference** | `SERVICE_NAME` | `inference-service` | 服务名称 |
| **Inference** | `SCHEDULER_URL` | `http://scheduler:8080` | 调度器地址 |
| **Inference** | `MAX_QUOTA` | `10` | 最大配额 |
| **Inference** | `GPU_TYPE` | `A10` | GPU 类型 |
| **Inference-Mock** | `SCHEDULER_URL` | `http://scheduler:8080` | 调度器地址 |
| **Inference-Mock** | `KAFKA_SERVERS` | `kafka:9092` | Kafka 地址 |

### 资源配置

| 服务 | CPU 请求 | CPU 限制 | 内存请求 | 内存限制 | GPU |
|------|----------|----------|----------|----------|-----|
| **Scheduler** | 500m | 2000m | 1Gi | 4Gi | - |
| **Inference** | 1000m | 4000m | 2Gi | 8Gi | 1 |
| **Inference-Mock** | 200m | 1000m | 512Mi | 2Gi | - |
| **RTSP-Server** | 200m | 1000m | 256Mi | 1Gi | - |
| **MongoDB** | 500m | 1000m | 1Gi | 2Gi | - |
| **Kafka** | 500m | 1000m | 1Gi | 2Gi | - |

### 存储配置

| 服务 | 存储类型 | 大小 | 挂载路径 | 说明 |
|------|----------|------|----------|------|
| **MongoDB** | PVC | 20Gi | `/data/db` | 数据库数据 |
| **Kafka** | PVC | 10Gi | `/var/lib/kafka/data` | 消息数据 |
| **Inference** | PVC | 50Gi | `/app/models` | 模型文件 |
| **RTSP-Server** | PVC | 5Gi | `/video` | 视频文件 |
| **Inference-Mock** | PVC | 1Gi | `/app/screenshots` | 截图文件 |

## 🔧 运维操作

### 扩缩容操作

```bash
# 扩展 Inference 服务
kubectl scale deployment inference --replicas=3 -n cv-system

# 扩展 Inference-Mock 服务
kubectl scale deployment inference-mock --replicas=2 -n cv-system

# 查看扩缩容状态
kubectl get deployment -n cv-system

# 查看 HPA 状态
kubectl get hpa -n cv-system
```

### 配置更新

```bash
# 更新 ConfigMap
kubectl apply -f 02-configmaps.yaml

# 重启服务以应用新配置
kubectl rollout restart deployment/scheduler -n cv-system
kubectl rollout restart deployment/inference -n cv-system

# 查看滚动更新状态
kubectl rollout status deployment/scheduler -n cv-system

# 回滚到上一个版本
kubectl rollout undo deployment/scheduler -n cv-system
```

### 日志查看

```bash
# 查看 Scheduler 日志
kubectl logs -f deployment/scheduler -n cv-system

# 查看 Inference 日志
kubectl logs -f deployment/inference -n cv-system

# 查看所有服务日志
kubectl logs -f -l tier=application -n cv-system

# 查看特定时间段的日志
kubectl logs deployment/scheduler -n cv-system --since=1h

# 导出日志到文件
kubectl logs deployment/scheduler -n cv-system > scheduler.log
```

### 故障排查

```bash
# 使用健康检查脚本
./scripts/health-check.sh

# 检查 Pod 状态
kubectl describe pod -l app=scheduler -n cv-system

# 检查服务连通性
kubectl exec -n cv-system deployment/scheduler -- nslookup mongodb
kubectl exec -n cv-system deployment/scheduler -- nslookup kafka

# 检查存储挂载
kubectl exec -n cv-system deployment/inference -- df -h
kubectl exec -n cv-system deployment/mongodb -- df -h

# 进入容器调试
kubectl exec -it deployment/scheduler -n cv-system -- bash

# 查看资源使用
kubectl top pods -n cv-system
kubectl top nodes
```

### 备份和恢复

```bash
# 备份 MongoDB 数据
kubectl create job mongodb-backup-manual --from=cronjob/mongodb-backup -n cv-system

# 备份配置
kubectl get all,configmap,secret,pvc -n cv-system -o yaml > cv-system-backup.yaml

# 恢复配置
kubectl apply -f cv-system-backup.yaml

# 查看备份任务状态
kubectl get jobs -n cv-system
```

### 清理环境

```bash
# 使用清理脚本
./scripts/cleanup.sh

# 保留数据的清理
./scripts/cleanup.sh --keep-namespace

# 完全清理 (包括数据)
./scripts/cleanup.sh --delete-data --force
```

## 🔐 安全配置

### RBAC 配置

```yaml
# 服务账户和权限配置
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cv-system-sa
  namespace: cv-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: cv-system
  name: cv-system-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
```

### 网络策略

```yaml
# 限制网络访问
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cv-system-network-policy
  namespace: cv-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: cv-system
```

## 📊 监控配置

### Prometheus 监控

```yaml
# ServiceMonitor 配置
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cv-system-monitor
  namespace: cv-system
spec:
  selector:
    matchLabels:
      monitoring: "true"
  endpoints:
  - port: http
    path: /actuator/prometheus
```

### Grafana 仪表板

- **Scheduler 监控**: 任务调度成功率、服务注册数量、响应时间
- **Inference 监控**: GPU 使用率、任务处理时间、内存使用
- **基础设施监控**: MongoDB 连接数、Kafka 消息积压、存储使用率

## 🚨 告警配置

### PrometheusRule 告警

```yaml
# 关键告警规则
groups:
- name: cv-system-alerts
  rules:
  - alert: SchedulerDown
    expr: up{job="scheduler"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Scheduler 服务不可用"
  
  - alert: InferenceHighMemory
    expr: container_memory_usage_bytes{pod=~"inference-.*"} / container_spec_memory_limit_bytes > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Inference 服务内存使用率过高"
```

## 🔄 备份恢复

### MongoDB 备份

```bash
# 创建备份任务
kubectl create job mongodb-backup --from=cronjob/mongodb-backup -n cv-system

# 手动备份
kubectl exec -n cv-system deployment/mongodb -- mongodump --out /backup/$(date +%Y%m%d_%H%M%S)
```

### 配置备份

```bash
# 备份所有配置
kubectl get all,configmap,secret,pvc -n cv-system -o yaml > cv-system-backup.yaml

# 恢复配置
kubectl apply -f cv-system-backup.yaml
```

## 📞 技术支持

### 常用命令

```bash
# 使用 Makefile (推荐)
make help                    # 查看所有可用命令
make build                   # 构建镜像
make deploy                  # 部署系统
make health                  # 健康检查
make clean                   # 清理环境

# 使用脚本
./scripts/deploy.sh          # 一键部署
./scripts/health-check.sh    # 健康检查
./scripts/cleanup.sh         # 清理环境
./scripts/build-images.sh    # 构建镜像

# 原生 kubectl 命令
kubectl get pods -n cv-system
kubectl top pods -n cv-system
kubectl logs -f deployment/scheduler -n cv-system
```

### 快速开始

1. **5分钟快速部署**: 查看 [QUICKSTART.md](./QUICKSTART.md)
2. **完整部署指南**: 查看本文档
3. **故障排查**: 查看 [故障排查手册](../troubleshooting/inference-scheduler-troubleshooting.md)

### 故障联系

- **紧急故障**: 运维团队 24/7 支持
- **技术咨询**: <EMAIL>
- **文档反馈**: <EMAIL>
- **GitHub Issues**: [项目 Issues](https://github.com/example/cv-system/issues)

### 相关文档

- 📚 [API 接口文档](../api/inference-scheduler-api.md)
- 🏗️ [系统架构文档](../inference-scheduler-interaction.md)
- 🔍 [故障排查手册](../troubleshooting/inference-scheduler-troubleshooting.md)
- 🚀 [快速开始指南](./QUICKSTART.md)

---

## 📋 部署清单

### ✅ 部署前检查

- [ ] Kubernetes 集群可用 (v1.20+)
- [ ] kubectl 已配置并可访问集群
- [ ] Docker 已安装 (用于构建镜像)
- [ ] 集群有足够资源 (至少 8GB 内存, 4 CPU)
- [ ] 有 GPU 节点 (如果部署 Inference 服务)
- [ ] 存储类可用 (或使用 hostPath)
- [ ] 文件序号正确 (运行 `./scripts/verify-files.sh` 检查)

### ✅ 部署后验证

- [ ] 所有 Pod 处于 Running 状态
- [ ] 所有 Service 有端点
- [ ] 健康检查通过 (`./scripts/health-check.sh`)
- [ ] 可以创建测试任务
- [ ] 日志无严重错误

### ✅ 生产环境额外要求

- [ ] 配置持久化存储 (非 hostPath)
- [ ] 设置资源限制和请求
- [ ] 配置 Ingress 和 TLS
- [ ] 启用监控和告警
- [ ] 配置备份策略
- [ ] 设置网络策略
- [ ] 配置 RBAC 权限

## 🔧 文件序号说明

文件已按照正确的部署顺序重新编号 (01-13)，详见 [FILE-MAPPING.md](./FILE-MAPPING.md)。

### 正确的文件序号

```
01-namespace.yaml      → 命名空间和权限
02-storage.yaml        → 持久化存储
03-configmaps.yaml     → 配置文件
04-secrets.yaml        → 密钥配置
05-zookeeper.yaml      → Zookeeper 服务
06-kafka.yaml          → Kafka 消息队列
07-mongodb.yaml        → MongoDB 数据库
08-scheduler.yaml      → Scheduler 调度器
09-inference.yaml      → Inference 推理服务
10-rtsp-server.yaml    → RTSP 视频服务器
11-inference-mock.yaml → Inference Mock 模拟服务
12-ingress.yaml        → Ingress 网络配置
13-monitoring.yaml     → 监控和告警配置
```

### 🔄 文件重命名

如果你的文件还是旧的序号 (如 00-, 20-, 30-, 40-)，请使用重命名脚本：

```bash
# 设置脚本权限
chmod +x scripts/*.sh

# 方式 1: 完整版重命名 (推荐)
./scripts/rename-files.sh

# 方式 2: 快速重命名
./scripts/quick-rename.sh

# 方式 3: 一键设置权限
./scripts/setup-permissions.sh
```

详细说明请查看 [RENAME-GUIDE.md](./RENAME-GUIDE.md)。

### 验证文件序号

```bash
# 验证文件序号和内容
./scripts/verify-files.sh

# 查看当前文件列表
ls -1 [0-9][0-9]-*.yaml
```

---

**注意**:
1. 文件序号已重新整理，确保按正确顺序部署
2. 在生产环境部署前，请根据实际资源情况调整配置参数
3. 建议先在测试环境验证部署流程
