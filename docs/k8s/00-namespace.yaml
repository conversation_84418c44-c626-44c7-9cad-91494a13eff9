# Namespace 配置
apiVersion: v1
kind: Namespace
metadata:
  name: cv-system
  labels:
    name: cv-system
    tier: application
    environment: production
  annotations:
    description: "CV 推理系统命名空间"
---
# ServiceAccount 配置
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cv-system-sa
  namespace: cv-system
  labels:
    app: cv-system
---
# Role 配置
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: cv-system
  name: cv-system-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
---
# RoleBinding 配置
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cv-system-rolebinding
  namespace: cv-system
subjects:
- kind: ServiceAccount
  name: cv-system-sa
  namespace: cv-system
roleRef:
  kind: Role
  name: cv-system-role
  apiGroup: rbac.authorization.k8s.io
