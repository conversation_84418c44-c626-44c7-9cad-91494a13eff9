# CV分析系统Docker部署环境变量配置
# 复制此文件为.env并根据实际环境修改配置

# ===========================================
# 基础服务端口配置
# ===========================================
SCHEDULER_PORT=8080
INFERENCE_MOCK_1_PORT=8081
INFERENCE_MOCK_2_PORT=8082

# ===========================================
# MongoDB配置 (外部MongoDB服务)
# ===========================================
# MongoDB连接URI，需要包含认证信息
MONGODB_URI=*************************************************************************
# MongoDB数据库名称
MONGODB_DATABASE=cv_scheduler

# ===========================================
# Redis配置 (可选，用于分布式锁)
# ===========================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ===========================================
# Kafka配置 (外部Kafka服务)
# ===========================================
# Kafka服务器地址
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
# Kafka主题名称
KAFKA_TOPIC=vision-events

# ===========================================
# 调度器配置
# ===========================================
# 调度器URL，用于inference-mock服务注册
SCHEDULER_URL=http://localhost:8080
# 锁类型: local(本地锁) 或 redis(分布式锁)
SCHEDULER_LOCK_TYPE=local
# 健康检查间隔(毫秒)
SCHEDULER_HEALTH_CHECK_INTERVAL=30000
# 健康检查超时(毫秒)
SCHEDULER_HEALTH_CHECK_TIMEOUT=5000

# ===========================================
# RTSP配置
# ===========================================
# RTSP服务器地址
RTSP_DEFAULT_SERVER=localhost:9554
# 是否启用RTSP模拟模式
RTSP_SIMULATION_MODE=true

# ===========================================
# 推理服务1配置
# ===========================================
INFERENCE_MOCK_1_NAME=inference-mock-1
INFERENCE_MOCK_1_REGION=default
INFERENCE_MOCK_1_GPU_TYPE=A10
INFERENCE_MOCK_1_MAX_QUOTA=10
INFERENCE_MOCK_1_ORCHESTRATION=YOLO_TRACKING_CLIP

# ===========================================
# 推理服务2配置 (可选)
# ===========================================
INFERENCE_MOCK_2_NAME=inference-mock-2
INFERENCE_MOCK_2_REGION=default
INFERENCE_MOCK_2_GPU_TYPE=A10
INFERENCE_MOCK_2_MAX_QUOTA=10
INFERENCE_MOCK_2_ORCHESTRATION=OVIT_CLIP

# ===========================================
# 部署选项
# ===========================================
# 是否启用多实例模式 (启动两个inference-mock服务)
ENABLE_MULTI_INSTANCE=false
