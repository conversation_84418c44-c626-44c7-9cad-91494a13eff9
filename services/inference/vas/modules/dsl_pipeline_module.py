import cv2
import time
import logging
import pyds
import numpy as np
from vas.common.dsl import *
from vas.entrypoints.task_interface import *
from vas.modules.base_module import *


uri_file = "/opt/nvidia/deepstream/deepstream/samples/streams/sample_1080p_h265.mp4"
# Filespecs (Jetson and dGPU) for the Primary GIE
primary_infer_config_file = '/opt/nvidia/deepstream/deepstream/samples/configs/deepstream-app/config_infer_primary.txt'
primary_model_engine_file = '/opt/nvidia/deepstream/deepstream/samples/triton_model_repo/Primary_Detector/1/resnet18_trafficcamnet.etlt_b1_gpu0_int8.engine'
# Filespec for the IOU Tracker config file
iou_tracker_config_file = '/opt/nvidia/deepstream/deepstream/samples/configs/deepstream-app/config_tracker_IOU.yml'

PGIE_CLASS_ID_VEHICLE = 0
PGIE_CLASS_ID_BICYCLE = 1
PGIE_CLASS_ID_PERSON = 2
PGIE_CLASS_ID_ROADSIGN = 3


# Function to be called on End-of-Stream (EOS) event
def eos_event_listener(client_data):
    dsl_pipeline = cast(client_data, POINTER(py_object)).contents.value
    dsl_pipeline.logger.info('Pipeline EOS event')
    # dsl_pipeline_stop('pipeline')
    dsl_main_loop_quit()
    dsl_pipeline.logger.info('pipeline main loop quit')

## 
# Function to be called on every change of Pipeline state
## 
def state_change_listener(old_state, new_state, client_data):
    dsl_pipeline = cast(client_data, POINTER(py_object)).contents.value
    dsl_pipeline.logger.info(f"previous state = {old_state}, new state = {new_state}")
    if new_state == DSL_STATE_PLAYING:
        dsl_pipeline_dump_to_dot('pipeline', "state-playing")


def new_buffer_handler_cb(data_type, buffer, user_data):
    dsl_pipeline = cast(user_data, POINTER(py_object)).contents.value
    frame_number = 0
    #Intiallizing object counter with 0.
    obj_counter = {
        PGIE_CLASS_ID_VEHICLE:0,
        PGIE_CLASS_ID_PERSON:0,
        PGIE_CLASS_ID_BICYCLE:0,
        PGIE_CLASS_ID_ROADSIGN:0
    }
    num_rects = 0

    # Retrieve batch metadata from the gst_buffer
    batch_meta = pyds.gst_buffer_get_nvds_batch_meta(buffer)
    l_frame = batch_meta.frame_meta_list
    while l_frame is not None:
        try:
            # Note that l_frame.data needs a cast to pyds.NvDsFrameMeta
            # The casting is done by pyds.glist_get_nvds_frame_meta()
            # The casting also keeps ownership of the underlying memory
            # in the C code, so the Python garbage collector will leave
            # it alone.
            frame_meta = pyds.glist_get_nvds_frame_meta(l_frame.data)
        except StopIteration:
            break

        n_frame = pyds.get_nvds_buf_surface(buffer, frame_meta.batch_id)
        if n_frame is None:
            dsl_pipeline.logger.warning(f"receive none frame")
            return
        # convert the python array into numpy array format.
        frame_image = np.array(n_frame, copy=True, order='C')
        # covert the array into cv2 default BGRA format
        frame_image = cv2.cvtColor(frame_image, cv2.COLOR_RGBA2BGR)

        frame_number = frame_meta.frame_num
        num_rects = frame_meta.num_obj_meta
        l_obj = frame_meta.obj_meta_list
        bboxes = []
        while l_obj is not None:
            try:
                # Casting l_obj.data to pyds.NvDsObjectMeta
                obj_meta = pyds.glist_get_nvds_object_meta(l_obj.data)

                # get object rect params
                rect_params = obj_meta.rect_params
                top = rect_params.top
                left = rect_params.left
                width = rect_params.width
                height = rect_params.height
                confidence = obj_meta.confidence
                class_id = obj_meta.class_id
                bbox = [left, top, width, height, confidence, class_id]
                bboxes.append(bbox)
            except StopIteration:
                break
            try: 
                l_obj = l_obj.next
            except StopIteration:
                break

        bboxes = np.array(bboxes).astype(np.float32)
        dsl_pipeline.logger.debug(f"[DSL] Frame {frame_number}: 检测到 {len(bboxes)} 个目标，图像尺寸: {frame_image.shape}")
        # 时间精确到毫秒
        meta_info = MetaInfo(frame_id=frame_number, timestamp=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) + f".{frame_meta.frame_num % 1000:03d}")
        frame_info = dsl_pipeline.prepare_frame_info(frame_image)
        exec_task_message = dsl_pipeline.prepare_execute_task_message(frame_info, meta_info, dsl_pipeline.task_info)
        dsl_pipeline.push_message_to_module(exec_task_message, dsl_pipeline.next_module)
        dsl_pipeline.logger.debug(f"[DSL] Frame {frame_number}: 推送到 {dsl_pipeline.next_module}")
        try:
            l_frame = l_frame.next
        except StopIteration:
            break
    return DSL_FLOW_OK


class DSLPipelineModule(BaseModule):

    def __init__(self, module_info, config, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_info, config, queue, logger, task_info)
        if task_info is None:
            task_config_file = self.config[self.module_type]["task_config_file"]
            self.task_info = self.load_task_from_config(task_config_file)

        algo_config = self.task_info.algorithmOrchestration
        if OrchestrationType.YOLO_TRACKING_CLIP == algo_config.orchestrationType:
            self.start_decode_pipeline(self.task_info.device)
            # self.next_module = YOLOV8_DETECTOR_MODULE
        else:
            self.start_decode_pipeline(self.task_info.device)
            # self.next_module = OWL_DETECTOR_MODULE


    def start_decode_pipeline(self, device: Device):
        stream_config = device.streamConfig
        decoder_config = stream_config.decoderConf
        skip_frames = 2 if decoder_config.keyFrameOnly else 0
        drop_frame_interval = decoder_config.decodeStep
        stream_url = stream_config.url

        # New rtsp stream Source using the filespec defined above
        # * `name` - [in] unique name for the new Source
        # * `uri` - [in] fully qualified URI prefixed with `rtsp://`
        # * `protocol` - [in] one of the [RTP Protocols](#rtp-protocols) constant values.
        # * `skip_frames` - [in] the type of frames to skip during decoding.
        # -   (0): decode_all       - Decode all frames
        # -   (1): decode_non_ref   - Decode non-ref frame
        # -   (2): decode_key       - decode key frames
        # * `drop_frame_interval` - [in] number of frames to drop between each decoded frame. 0 = decode all frames.
        # * `latency` - [in] source latency setting in milliseconds, equates to the amount of data to buffer. 
        # * `timeout` - [in] maximum time between successive frame buffers in units of seconds before initiating a "reconnection-cycle". Set to 0 to disable the timeout.
        ret_val = dsl_source_rtsp_new('rtsp-source', 
            stream_url, DSL_RTP_ALL, skip_frames, drop_frame_interval, 1000, 10)
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"create rtsp source from {stream_url} fail")
            return ret_val

        # IMPORTANT! We must set the buffer format to RGBA for each source.
        retval = dsl_source_video_buffer_out_format_set('rtsp-source', 
            DSL_VIDEO_FORMAT_RGBA)
        if retval != DSL_RETURN_SUCCESS:
            return ret_val

        # New App Sink created to provide new buffers to process by calling
        # the new_buffer_handler_cb callback function defined above.
        ret_val = dsl_sink_app_new('app-sink', 
            DSL_SINK_APP_DATA_TYPE_BUFFER, new_buffer_handler_cb, self)
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"create app-sink fail")
            return ret_val

        # Add all the components to our pipeline
        ret_val = dsl_pipeline_new_component_add_many('pipeline', 
            ['rtsp-source', 'app-sink', None])
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"dsl pipeline add many components fail")
            return ret_val

        # If using the new Streammux, then change the memory type of each source
        if dsl_info_use_new_nvstreammux_get():
            ret_val = dsl_component_nvbuf_mem_type_set_many(
                ['rtsp-source', ], DSL_NVBUF_MEM_TYPE_CUDA_UNIFIED)
        # if using the old Streammux we set the memtype of the Streammux itself.    
        else:
            ret_val = dsl_pipeline_streammux_nvbuf_mem_type_set('pipeline',
                DSL_NVBUF_MEM_TYPE_CUDA_UNIFIED)
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"set nvbuf memtype fail")
            return ret_val

        # Add the listener callback functions defined above
        ret_val = dsl_pipeline_state_change_listener_add('pipeline', state_change_listener, self)
        if ret_val != DSL_RETURN_SUCCESS:
            return ret_val
        ret_val = dsl_pipeline_eos_listener_add('pipeline', eos_event_listener, self)
        if ret_val != DSL_RETURN_SUCCESS:
            return ret_val

        # Play the pipeline
        ret_val = dsl_pipeline_play('pipeline')
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"dsl pipeline play fail")
            return ret_val

        return ret_val

    
    def start_track_pipeline(self, device: Device):
        stream_config = device.streamConfig
        decoder_config = stream_config.decoderConf
        skip_frames = 2 if decoder_config.keyFrameOnly else 0
        drop_frame_interval = decoder_config.decodeStep
        stream_url = stream_config.url

        # New rtsp stream Source using the filespec defined above
        # * `name` - [in] unique name for the new Source
        # * `uri` - [in] fully qualified URI prefixed with `rtsp://`
        # * `protocol` - [in] one of the [RTP Protocols](#rtp-protocols) constant values.
        # * `skip_frames` - [in] the type of frames to skip during decoding.
        # -   (0): decode_all       - Decode all frames
        # -   (1): decode_non_ref   - Decode non-ref frame
        # -   (2): decode_key       - decode key frames
        # * `drop_frame_interval` - [in] number of frames to drop between each decoded frame. 0 = decode all frames.
        # * `latency` - [in] source latency setting in milliseconds, equates to the amount of data to buffer. 
        # * `timeout` - [in] maximum time between successive frame buffers in units of seconds before initiating a "reconnection-cycle". Set to 0 to disable the timeout.
        ret_val = dsl_source_rtsp_new('rtsp-source', 
            stream_url, DSL_RTP_ALL, skip_frames, drop_frame_interval, 1000, 10)
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"create rtsp source from {stream_url} fail")
            return ret_val

        # IMPORTANT! We must set the buffer format to RGBA for each source.
        retval = dsl_source_video_buffer_out_format_set('rtsp-source', 
            DSL_VIDEO_FORMAT_RGBA)
        if retval != DSL_RETURN_SUCCESS:
            return ret_val

        # New Primary GIE using the filespecs above with interval = 0
        ret_val = dsl_infer_gie_primary_new('primary-gie', 
            primary_infer_config_file, primary_model_engine_file, 0)
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"create primary-gie from {primary_infer_config_file} {primary_model_engine_file} fail")
            return ret_val

        # New IOU Tracker, setting operational width and hieght
        ret_val = dsl_tracker_new('iou-tracker', 
            iou_tracker_config_file, 480, 272)
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"create iou-tracker from {iou_tracker_config_file} fail")
            return ret_val

        # New OSD with text, clock and bbox display all enabled. 
        retval = dsl_osd_new('on-screen-display', 
            text_enabled=True, clock_enabled=True, bbox_enabled=True, mask_enabled=False)
        if retval != DSL_RETURN_SUCCESS:
            return ret_val

        # New App Sink created to provide new buffers to process by calling
        # the new_buffer_handler_cb callback function defined above.
        ret_val = dsl_sink_app_new('app-sink', 
            DSL_SINK_APP_DATA_TYPE_BUFFER, new_buffer_handler_cb, self)
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"create app-sink fail")
            return ret_val

        # Add all the components to our pipeline
        ret_val = dsl_pipeline_new_component_add_many('pipeline', 
            ['rtsp-source', 'primary-gie', 'iou-tracker', 'on-screen-display', 'app-sink', None])
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"dsl pipeline add many components fail")
            return ret_val

        # If using the new Streammux, then change the memory type of each source
        if dsl_info_use_new_nvstreammux_get():
            ret_val = dsl_component_nvbuf_mem_type_set_many(
                ['rtsp-source', ], DSL_NVBUF_MEM_TYPE_CUDA_UNIFIED)
        # if using the old Streammux we set the memtype of the Streammux itself.    
        else:
            ret_val = dsl_pipeline_streammux_nvbuf_mem_type_set('pipeline',
                DSL_NVBUF_MEM_TYPE_CUDA_UNIFIED)
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"set nvbuf memtype fail")
            return ret_val

        # Add the listener callback functions defined above
        ret_val = dsl_pipeline_state_change_listener_add('pipeline', state_change_listener, self)
        if ret_val != DSL_RETURN_SUCCESS:
            return ret_val
        ret_val = dsl_pipeline_eos_listener_add('pipeline', eos_event_listener, self)
        if ret_val != DSL_RETURN_SUCCESS:
            return ret_val

        # Play the pipeline
        ret_val = dsl_pipeline_play('pipeline')
        if ret_val != DSL_RETURN_SUCCESS:
            self.logger.error(f"dsl pipeline play fail")
            return ret_val

        return ret_val


    def event_loop(self):
        try:
            dsl_main_loop_run()
        except Exception:
            last_traceback = get_exception_traceback()
            self.logger.error(f"error: {last_traceback}")
        dsl_pipeline_delete_all()
        self.logger.info(f"delete dsl pipeline ...")
        # dsl_component_delete_all()
        self.logger.info(f"Program exit...")