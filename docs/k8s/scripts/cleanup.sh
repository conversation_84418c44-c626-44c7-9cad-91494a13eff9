#!/bin/bash

# CV 推理系统 Kubernetes 清理脚本
# 作者: CV System Team
# 版本: v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 确认操作
confirm_cleanup() {
    echo "⚠️  警告: 此操作将删除 CV 推理系统的所有资源"
    echo "包括:"
    echo "  - 所有 Pod 和 Deployment"
    echo "  - 所有 Service 和 Ingress"
    echo "  - 所有 ConfigMap 和 Secret"
    echo "  - 所有 PVC 和数据 (如果选择删除数据)"
    echo
    
    if [ "$FORCE" = true ]; then
        log_warning "强制模式，跳过确认"
        return 0
    fi
    
    read -p "确定要继续吗? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 删除应用服务
cleanup_applications() {
    log_info "清理应用服务..."
    
    # 删除应用 Deployment 和 Service
    kubectl delete -f ../20-scheduler.yaml --ignore-not-found=true
    kubectl delete -f ../21-inference.yaml --ignore-not-found=true
    kubectl delete -f ../22-rtsp-server.yaml --ignore-not-found=true
    kubectl delete -f ../23-inference-mock.yaml --ignore-not-found=true
    
    # 等待 Pod 终止
    log_info "等待 Pod 终止..."
    kubectl wait --for=delete pod -l tier=application -n cv-system --timeout=120s || true
    
    log_success "应用服务清理完成"
}

# 删除基础设施
cleanup_infrastructure() {
    log_info "清理基础设施服务..."
    
    # 删除基础设施 Deployment 和 Service
    kubectl delete -f ../10-zookeeper.yaml --ignore-not-found=true
    kubectl delete -f ../11-kafka.yaml --ignore-not-found=true
    kubectl delete -f ../12-mongodb.yaml --ignore-not-found=true
    
    # 等待 Pod 终止
    log_info "等待基础设施 Pod 终止..."
    kubectl wait --for=delete pod -l tier=infrastructure -n cv-system --timeout=120s || true
    
    log_success "基础设施服务清理完成"
}

# 删除网络配置
cleanup_networking() {
    log_info "清理网络配置..."
    
    kubectl delete -f ../30-ingress.yaml --ignore-not-found=true
    
    log_success "网络配置清理完成"
}

# 删除监控配置
cleanup_monitoring() {
    log_info "清理监控配置..."
    
    kubectl delete -f ../40-monitoring.yaml --ignore-not-found=true
    
    log_success "监控配置清理完成"
}

# 删除配置和密钥
cleanup_configs() {
    log_info "清理配置和密钥..."
    
    kubectl delete -f ../02-configmaps.yaml --ignore-not-found=true
    kubectl delete -f ../03-secrets.yaml --ignore-not-found=true
    
    log_success "配置和密钥清理完成"
}

# 删除存储
cleanup_storage() {
    if [ "$DELETE_DATA" = true ]; then
        log_warning "删除持久化存储和数据..."
        kubectl delete -f ../01-storage.yaml --ignore-not-found=true
        log_warning "所有数据已删除"
    else
        log_info "保留持久化存储和数据"
        # 只删除 PVC，保留 PV
        kubectl delete pvc --all -n cv-system --ignore-not-found=true
        log_info "PVC 已删除，PV 和数据已保留"
    fi
}

# 删除命名空间
cleanup_namespace() {
    if [ "$KEEP_NAMESPACE" = true ]; then
        log_info "保留命名空间 cv-system"
    else
        log_info "删除命名空间..."
        kubectl delete -f ../00-namespace.yaml --ignore-not-found=true
        
        # 等待命名空间删除
        log_info "等待命名空间删除..."
        while kubectl get namespace cv-system &> /dev/null; do
            sleep 2
        done
        
        log_success "命名空间删除完成"
    fi
}

# 清理孤儿资源
cleanup_orphaned_resources() {
    log_info "清理孤儿资源..."
    
    # 清理可能残留的资源
    kubectl delete jobs --all -n cv-system --ignore-not-found=true
    kubectl delete cronjobs --all -n cv-system --ignore-not-found=true
    kubectl delete hpa --all -n cv-system --ignore-not-found=true
    kubectl delete pdb --all -n cv-system --ignore-not-found=true
    kubectl delete networkpolicies --all -n cv-system --ignore-not-found=true
    
    # 清理 Finalizers (如果有卡住的资源)
    for resource in pods deployments services configmaps secrets pvc; do
        kubectl get $resource -n cv-system -o name 2>/dev/null | while read -r res; do
            kubectl patch $res -n cv-system -p '{"metadata":{"finalizers":[]}}' --type=merge 2>/dev/null || true
        done
    done
    
    log_success "孤儿资源清理完成"
}

# 验证清理结果
verify_cleanup() {
    log_info "验证清理结果..."
    
    # 检查命名空间中的资源
    if kubectl get namespace cv-system &> /dev/null; then
        local remaining_resources=$(kubectl get all -n cv-system 2>/dev/null | wc -l)
        if [ "$remaining_resources" -gt 1 ]; then
            log_warning "命名空间中仍有 $((remaining_resources-1)) 个资源"
            kubectl get all -n cv-system
        else
            log_success "命名空间已清空"
        fi
    else
        log_success "命名空间已删除"
    fi
    
    # 检查 PV 状态
    local pvs=$(kubectl get pv -l app=cv-system 2>/dev/null | wc -l)
    if [ "$pvs" -gt 1 ]; then
        if [ "$DELETE_DATA" = true ]; then
            log_warning "仍有 $((pvs-1)) 个 PV 存在"
        else
            log_info "保留了 $((pvs-1)) 个 PV"
        fi
        kubectl get pv -l app=cv-system
    fi
}

# 显示清理后信息
show_cleanup_info() {
    echo
    log_info "=== 清理完成 ==="
    
    if [ "$DELETE_DATA" = true ]; then
        echo "✅ 所有资源和数据已删除"
    else
        echo "✅ 应用资源已删除"
        echo "📁 数据已保留在 PV 中"
        echo
        echo "如需重新部署，数据将自动恢复"
    fi
    
    if [ "$KEEP_NAMESPACE" = true ]; then
        echo "📁 命名空间 cv-system 已保留"
    fi
    
    echo
    echo "🔧 有用的命令:"
    echo "  查看剩余资源: kubectl get all -n cv-system"
    echo "  查看 PV:     kubectl get pv"
    echo "  重新部署:    ./deploy.sh"
    echo
}

# 主函数
main() {
    echo "🧹 开始清理 CV 推理系统"
    echo "========================"
    
    # 解析参数
    FORCE=false
    DELETE_DATA=false
    KEEP_NAMESPACE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                FORCE=true
                shift
                ;;
            --delete-data)
                DELETE_DATA=true
                shift
                ;;
            --keep-namespace)
                KEEP_NAMESPACE=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --force            强制执行，不询问确认"
                echo "  --delete-data      删除持久化数据"
                echo "  --keep-namespace   保留命名空间"
                echo "  -h, --help         显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 检查 kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    # 检查命名空间是否存在
    if ! kubectl get namespace cv-system &> /dev/null; then
        log_warning "命名空间 cv-system 不存在，无需清理"
        exit 0
    fi
    
    # 确认操作
    confirm_cleanup
    
    # 执行清理步骤
    cleanup_monitoring
    cleanup_networking
    cleanup_applications
    cleanup_infrastructure
    cleanup_configs
    cleanup_storage
    cleanup_orphaned_resources
    
    if [ "$KEEP_NAMESPACE" = false ]; then
        cleanup_namespace
    fi
    
    verify_cleanup
    show_cleanup_info
    
    log_success "🎉 清理完成！"
}

# 错误处理
trap 'log_error "清理过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
