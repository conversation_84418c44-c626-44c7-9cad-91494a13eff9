# RTSP Server Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rtsp-server
  namespace: cv-system
  labels:
    app: rtsp-server
    tier: infrastructure
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rtsp-server
  template:
    metadata:
      labels:
        app: rtsp-server
        tier: infrastructure
    spec:
      serviceAccountName: cv-system-sa
      containers:
      - name: rtsp-server
        image: cv/rtsp-server:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9554
          name: rtsp
          protocol: TCP
        - containerPort: 8889
          name: api
          protocol: TCP
        - containerPort: 8888
          name: webrtc
          protocol: TCP
        env:
        - name: TZ
          value: "Asia/Shanghai"
        - name: MEDIAMTX_CONFIG_FILE
          value: "/mediamtx.yml"
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        volumeMounts:
        - name: rtsp-config
          mountPath: /mediamtx.yml
          subPath: mediamtx.yml
          readOnly: true
        - name: rtsp-video
          mountPath: /video
          readOnly: true
        - name: rtsp-logs
          mountPath: /logs
        livenessProbe:
          httpGet:
            path: /v3/config/global/get
            port: 8889
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /v3/config/global/get
            port: 8889
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /v3/config/global/get
            port: 8889
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 12
      volumes:
      - name: rtsp-config
        configMap:
          name: rtsp-server-config
      - name: rtsp-video
        persistentVolumeClaim:
          claimName: rtsp-video-pvc
      - name: rtsp-logs
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
# RTSP Server Service
apiVersion: v1
kind: Service
metadata:
  name: rtsp-server
  namespace: cv-system
  labels:
    app: rtsp-server
    tier: infrastructure
spec:
  type: ClusterIP
  ports:
  - port: 9554
    targetPort: 9554
    protocol: TCP
    name: rtsp
  - port: 8889
    targetPort: 8889
    protocol: TCP
    name: api
  - port: 8888
    targetPort: 8888
    protocol: TCP
    name: webrtc
  selector:
    app: rtsp-server
---
# RTSP Server NodePort Service (用于外部访问)
apiVersion: v1
kind: Service
metadata:
  name: rtsp-server-external
  namespace: cv-system
  labels:
    app: rtsp-server
    tier: infrastructure
    type: external
spec:
  type: NodePort
  ports:
  - port: 9554
    targetPort: 9554
    protocol: TCP
    name: rtsp
    nodePort: 30554  # 外部可通过 <NodeIP>:30554 访问
  - port: 8889
    targetPort: 8889
    protocol: TCP
    name: api
    nodePort: 30889  # API 端口
  selector:
    app: rtsp-server
---
# RTSP 视频文件初始化 Job
apiVersion: batch/v1
kind: Job
metadata:
  name: rtsp-video-init
  namespace: cv-system
  labels:
    app: rtsp-server
    type: init
spec:
  template:
    metadata:
      labels:
        app: rtsp-server
        type: init
    spec:
      serviceAccountName: cv-system-sa
      restartPolicy: OnFailure
      containers:
      - name: video-init
        image: alpine:3.18
        command:
        - sh
        - -c
        - |
          echo "初始化视频文件..."
          
          # 创建目录
          mkdir -p /video
          
          # 检查是否已有视频文件
          if [ -f "/video/knight.mov" ]; then
            echo "视频文件已存在，跳过初始化"
            exit 0
          fi
          
          # 安装 wget
          apk add --no-cache wget
          
          # 下载示例视频文件 (这里使用一个公开的测试视频)
          echo "下载示例视频文件..."
          wget -O /video/knight.mov "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" || {
            echo "下载失败，创建占位文件"
            touch /video/knight.mov
          }
          
          # 创建其他测试文件的软链接
          ln -sf knight.mov /video/test.mov
          ln -sf knight.mov /video/stream1.mov
          ln -sf knight.mov /video/camera1.mov
          
          echo "视频文件初始化完成"
          ls -la /video/
        volumeMounts:
        - name: rtsp-video
          mountPath: /video
      volumes:
      - name: rtsp-video
        persistentVolumeClaim:
          claimName: rtsp-video-pvc
      backoffLimit: 3
---
# RTSP 流推送 CronJob (定期推送视频流)
apiVersion: batch/v1
kind: CronJob
metadata:
  name: rtsp-stream-push
  namespace: cv-system
  labels:
    app: rtsp-server
    type: stream-push
spec:
  schedule: "*/5 * * * *"  # 每5分钟检查一次流状态
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: rtsp-server
            type: stream-push
        spec:
          serviceAccountName: cv-system-sa
          restartPolicy: OnFailure
          containers:
          - name: stream-push
            image: alpine:3.18
            command:
            - sh
            - -c
            - |
              # 安装 ffmpeg 和 curl
              apk add --no-cache ffmpeg curl
              
              # 检查 MediaMTX 是否运行
              if ! curl -s http://rtsp-server:8889/v3/config/global/get > /dev/null; then
                echo "MediaMTX 服务不可用"
                exit 1
              fi
              
              # 检查视频文件
              if [ ! -f "/video/knight.mov" ]; then
                echo "视频文件不存在"
                exit 1
              fi
              
              echo "开始推送 RTSP 流..."
              
              # 推送主流
              ffmpeg -re -stream_loop -1 -i /video/knight.mov \
                -c:v libx264 -preset ultrafast -tune zerolatency \
                -c:a aac -ar 44100 -ac 2 \
                -f rtsp rtsp://rtsp-server:9554/knight &
              
              # 推送测试流
              ffmpeg -re -stream_loop -1 -i /video/knight.mov \
                -c:v libx264 -preset ultrafast -tune zerolatency \
                -c:a aac -ar 44100 -ac 2 \
                -f rtsp rtsp://rtsp-server:9554/test &
              
              # 等待一段时间后停止
              sleep 60
              pkill ffmpeg
              
              echo "流推送完成"
            volumeMounts:
            - name: rtsp-video
              mountPath: /video
              readOnly: true
          volumes:
          - name: rtsp-video
            persistentVolumeClaim:
              claimName: rtsp-video-pvc
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
