from __future__ import annotations
from typing import List, Optional, Any, Dict
from pydantic import BaseModel, Field
from vas.entrypoints.task_interface import *

# ----------------- 基础子结构 -----------------

class BoundingBox(BaseModel):
    x: int
    y: int
    width: int
    height: int

class Polygon(BaseModel):
    points: List[Point]

class Line(BaseModel):
    startPoint: Point
    endPoint: Point
    direction: Optional[Direction] = None

class EntityAttribute(BaseModel):
    attributeName: str                                             # 分类标签, 只有分类时才有值
    attributeValue: Any                                            # 暂时不填
    confidence: float                                              # 分类得分
    algorithmId: str                                               # 微模型id或者zeroshot_clip

class PartOf(BaseModel):
    algorithmId: str
    confidence: float
    entities: List[str]  # 实体 ID 列表

class RelationNameValue(BaseModel):
    confidence: float
    entities: Optional[List[str]] = None
    algorithmId: str
    attributes: Optional[Dict[str, Any]] = None

# ----------------- 实体 -----------------

class EntityInstance(BaseModel):
    entityInstanceId: str                                            # track id
    entityType: str                                                  # object label
    algorithmId: str                                                 # 从algo配置取
    boundingBox: Optional[BoundingBox] = None                        # bbox
    polygon: Optional[Polygon] = None                                # 暂时不用
    line: Optional[Line] = None                                      # 暂时不用
    partOf: Optional[PartOf] = None                                  # 暂时不用
    entityAttributes: Optional[List[EntityAttribute]] = None         # 只有分类时, 该字段才有
    entityRelationship: Optional[RelationNameValue] = None           # 暂时不用
    externalInfo: Optional[Dict[str, Any]] = None                    # 暂时不用

# ----------------- 任务信息 -----------------

class TaskInfo(BaseModel):
    taskId: str
    eventTypeId: str
    orchestrationId: str
    orchestrationType: str
    taskLevel: str
    deviceName: str
    frameId: str
    externalInfo: Optional[Dict[str, Any]] = None

# ----------------- 原子事件 -----------------

class AtomicEventInstance(BaseModel):
    atomicEventInstanceId: str                                    # 唯一id, 自己生成
    eventTypeId: str                                              # taskMeta中获取
    taskId: str                                                   # task info获取
    deviceId: str                                                 # device中获取
    timestamp: int                                                # frame解码时间
    imageUri: str                                                 # 保存图片的url地址
    entities: List[EntityInstance]                                # 检测结果
    relationEntities: Optional[List[EntityInstance]] = None       # 暂时不用
    taskInfo: TaskInfo                                            # 根据实际字段生成