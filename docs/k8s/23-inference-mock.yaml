# Inference Mock Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: inference-mock
  namespace: cv-system
  labels:
    app: inference-mock
    tier: application
spec:
  replicas: 1
  selector:
    matchLabels:
      app: inference-mock
  template:
    metadata:
      labels:
        app: inference-mock
        tier: application
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8081"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: cv-system-sa
      containers:
      - name: inference-mock
        image: cv/inference-mock:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8081
          name: http
          protocol: TCP
        env:
        - name: SERVICE_NAME
          value: "inference-mock-1"
        - name: SERVICE_PORT
          value: "8081"
        - name: SCHEDULER_URL
          value: "http://scheduler:8080"
        - name: KAFKA_SERVERS
          value: "kafka:9092"
        - name: RTSP_DEFAULT_SERVER
          value: "rtsp-server:9554"
        - name: RTSP_SIMULATION_MODE
          value: "true"
        - name: MAX_QUOTA
          value: "10"
        - name: REGION
          value: "default"
        - name: GPU_TYPE
          value: "A10"
        - name: CURRENT_ORCHESTRATION
          value: "YOLO_TRACKING_CLIP"
        - name: PYTHONPATH
          value: "/app"
        - name: PYTHONUNBUFFERED
          value: "1"
        - name: TZ
          value: "Asia/Shanghai"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: inference-mock-config
          mountPath: /app/config
          readOnly: true
        - name: inference-mock-screenshots
          mountPath: /app/screenshots
        - name: inference-mock-logs
          mountPath: /app/logs
        - name: inference-mock-data
          mountPath: /app/data
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 12
      volumes:
      - name: inference-mock-config
        configMap:
          name: inference-mock-config
      - name: inference-mock-screenshots
        persistentVolumeClaim:
          claimName: inference-mock-screenshots-pvc
      - name: inference-mock-logs
        emptyDir: {}
      - name: inference-mock-data
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
# Inference Mock Service
apiVersion: v1
kind: Service
metadata:
  name: inference-mock
  namespace: cv-system
  labels:
    app: inference-mock
    tier: application
    monitoring: "true"
spec:
  type: ClusterIP
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: http
  selector:
    app: inference-mock
---
# Inference Mock 多实例部署 (可选)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: inference-mock-2
  namespace: cv-system
  labels:
    app: inference-mock-2
    tier: application
    instance: "2"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: inference-mock-2
  template:
    metadata:
      labels:
        app: inference-mock-2
        tier: application
        instance: "2"
    spec:
      serviceAccountName: cv-system-sa
      containers:
      - name: inference-mock-2
        image: cv/inference-mock:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8081
          name: http
          protocol: TCP
        env:
        - name: SERVICE_NAME
          value: "inference-mock-2"
        - name: SERVICE_PORT
          value: "8081"
        - name: SCHEDULER_URL
          value: "http://scheduler:8080"
        - name: KAFKA_SERVERS
          value: "kafka:9092"
        - name: RTSP_DEFAULT_SERVER
          value: "rtsp-server:9554"
        - name: RTSP_SIMULATION_MODE
          value: "true"
        - name: MAX_QUOTA
          value: "5"
        - name: REGION
          value: "default"
        - name: GPU_TYPE
          value: "A10"
        - name: CURRENT_ORCHESTRATION
          value: "OVIT_CLIP"
        - name: PYTHONPATH
          value: "/app"
        - name: PYTHONUNBUFFERED
          value: "1"
        - name: TZ
          value: "Asia/Shanghai"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: inference-mock-config
          mountPath: /app/config
          readOnly: true
        - name: inference-mock-screenshots
          mountPath: /app/screenshots
        - name: inference-mock-logs
          mountPath: /app/logs
        - name: inference-mock-data
          mountPath: /app/data
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: inference-mock-config
        configMap:
          name: inference-mock-config
      - name: inference-mock-screenshots
        emptyDir: {}
      - name: inference-mock-logs
        emptyDir: {}
      - name: inference-mock-data
        emptyDir: {}
      restartPolicy: Always
---
# Inference Mock 2 Service
apiVersion: v1
kind: Service
metadata:
  name: inference-mock-2
  namespace: cv-system
  labels:
    app: inference-mock-2
    tier: application
spec:
  type: ClusterIP
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: http
  selector:
    app: inference-mock-2
---
# Inference Mock HorizontalPodAutoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: inference-mock-hpa
  namespace: cv-system
  labels:
    app: inference-mock
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: inference-mock
  minReplicas: 1
  maxReplicas: 3
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
