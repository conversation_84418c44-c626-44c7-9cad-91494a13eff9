# Scheduler Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scheduler
  namespace: cv-system
  labels:
    app: scheduler
    tier: application
spec:
  replicas: 2
  selector:
    matchLabels:
      app: scheduler
  template:
    metadata:
      labels:
        app: scheduler
        tier: application
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      serviceAccountName: cv-system-sa
      containers:
      - name: scheduler
        image: cv/scheduler:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        env:
        - name: SERVER_PORT
          value: "8080"
        - name: MONGODB_URI
          value: "***********************************************************************"
        - name: MONGODB_DATABASE
          value: "cv_scheduler"
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: JAVA_OPTS
          value: "-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
        - name: TZ
          value: "Asia/Shanghai"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: scheduler-config
          mountPath: /app/config
          readOnly: true
        - name: scheduler-logs
          mountPath: /app/logs
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 12
      volumes:
      - name: scheduler-config
        configMap:
          name: scheduler-config
      - name: scheduler-logs
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
# Scheduler Service
apiVersion: v1
kind: Service
metadata:
  name: scheduler
  namespace: cv-system
  labels:
    app: scheduler
    tier: application
    monitoring: "true"
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: scheduler
---
# Scheduler Headless Service (用于服务发现)
apiVersion: v1
kind: Service
metadata:
  name: scheduler-headless
  namespace: cv-system
  labels:
    app: scheduler
    tier: application
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: scheduler
---
# Scheduler HorizontalPodAutoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: scheduler-hpa
  namespace: cv-system
  labels:
    app: scheduler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: scheduler
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
---
# Scheduler PodDisruptionBudget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: scheduler-pdb
  namespace: cv-system
  labels:
    app: scheduler
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: scheduler
