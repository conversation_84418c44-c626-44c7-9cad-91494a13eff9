# MongoDB Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
  namespace: cv-system
  labels:
    app: mongodb
    tier: infrastructure
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
        tier: infrastructure
    spec:
      serviceAccountName: cv-system-sa
      containers:
      - name: mongodb
        image: mongo:6.0
        ports:
        - containerPort: 27017
          name: mongodb
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: password
        - name: MONGO_INITDB_DATABASE
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: database
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
        - name: mongodb-config
          mountPath: /data/configdb
        - name: mongodb-init
          mountPath: /docker-entrypoint-initdb.d
        livenessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: mongodb-data
        persistentVolumeClaim:
          claimName: mongodb-pvc
      - name: mongodb-config
        emptyDir: {}
      - name: mongodb-init
        configMap:
          name: mongodb-init-script
      restartPolicy: Always
---
# MongoDB Service
apiVersion: v1
kind: Service
metadata:
  name: mongodb
  namespace: cv-system
  labels:
    app: mongodb
    tier: infrastructure
spec:
  type: ClusterIP
  ports:
  - port: 27017
    targetPort: 27017
    protocol: TCP
    name: mongodb
  selector:
    app: mongodb
---
# MongoDB 初始化脚本 ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: mongodb-init-script
  namespace: cv-system
  labels:
    app: mongodb
data:
  init-db.js: |
    // 切换到 cv_scheduler 数据库
    db = db.getSiblingDB('cv_scheduler');
    
    // 创建用户
    db.createUser({
      user: 'scheduler',
      pwd: 'scheduler_password',
      roles: [
        {
          role: 'readWrite',
          db: 'cv_scheduler'
        }
      ]
    });
    
    // 创建集合和索引
    
    // 推理服务集合
    db.createCollection('inference_services');
    db.inference_services.createIndex({ "serviceId": 1 }, { unique: true });
    db.inference_services.createIndex({ "serviceName": 1 });
    db.inference_services.createIndex({ "region": 1 });
    db.inference_services.createIndex({ "status": 1 });
    db.inference_services.createIndex({ "lastHeartbeat": 1 });
    
    // 任务分配集合
    db.createCollection('task_allocations');
    db.task_allocations.createIndex({ "taskId": 1 }, { unique: true });
    db.task_allocations.createIndex({ "serviceId": 1 });
    db.task_allocations.createIndex({ "taskStatus": 1 });
    db.task_allocations.createIndex({ "createTime": 1 });
    db.task_allocations.createIndex({ "serviceId": 1, "taskStatus": 1 });
    
    // 调度策略集合
    db.createCollection('schedule_strategies');
    db.schedule_strategies.createIndex({ "strategyName": 1 }, { unique: true });
    
    // 系统配置集合
    db.createCollection('system_configs');
    db.system_configs.createIndex({ "configKey": 1 }, { unique: true });
    
    // 插入默认配置
    db.system_configs.insertMany([
      {
        configKey: "default_strategy",
        configValue: "FILL_FIRST",
        description: "默认调度策略",
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        configKey: "health_check_interval",
        configValue: "30000",
        description: "健康检查间隔(毫秒)",
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        configKey: "max_retry_attempts",
        configValue: "3",
        description: "最大重试次数",
        createTime: new Date(),
        updateTime: new Date()
      }
    ]);
    
    print("数据库初始化完成");
---
# MongoDB 备份 CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: mongodb-backup
  namespace: cv-system
  labels:
    app: mongodb
    type: backup
spec:
  schedule: "0 2 * * *"  # 每天凌晨2点执行备份
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: mongodb
            type: backup
        spec:
          serviceAccountName: cv-system-sa
          restartPolicy: OnFailure
          containers:
          - name: mongodb-backup
            image: mongo:6.0
            command:
            - sh
            - -c
            - |
              BACKUP_DIR="/backup/$(date +%Y%m%d_%H%M%S)"
              mkdir -p $BACKUP_DIR
              
              echo "开始备份 MongoDB..."
              mongodump --host mongodb:27017 \
                --username admin \
                --password password123 \
                --authenticationDatabase admin \
                --db cv_scheduler \
                --out $BACKUP_DIR
              
              echo "备份完成: $BACKUP_DIR"
              
              # 清理7天前的备份
              find /backup -type d -mtime +7 -exec rm -rf {} +
            env:
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: mongodb-secret
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mongodb-secret
                  key: password
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            emptyDir: {}  # 生产环境应该使用持久化存储
