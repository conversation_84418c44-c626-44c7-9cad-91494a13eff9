#!/bin/bash

# CV 推理系统 Kubernetes 一键部署脚本
# 作者: CV System Team
# 版本: v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装，请先安装 kubectl"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群，请检查 kubeconfig"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查镜像
check_images() {
    log_info "检查 Docker 镜像..."
    
    local images=(
        "cv/scheduler:latest"
        "cv/inference:latest"
        "cv/inference-mock:latest"
        "cv/rtsp-server:latest"
    )
    
    for image in "${images[@]}"; do
        if ! docker image inspect "$image" &> /dev/null; then
            log_warning "镜像 $image 不存在，请先构建镜像"
        else
            log_success "镜像 $image 存在"
        fi
    done
}

# 创建命名空间
create_namespace() {
    log_info "创建命名空间..."
    kubectl apply -f ../00-namespace.yaml
    log_success "命名空间创建完成"
}

# 部署存储
deploy_storage() {
    log_info "部署存储配置..."
    kubectl apply -f ../01-storage.yaml
    
    # 等待 PVC 绑定
    log_info "等待 PVC 绑定..."
    kubectl wait --for=condition=Bound pvc --all -n cv-system --timeout=300s
    log_success "存储配置部署完成"
}

# 部署配置
deploy_configs() {
    log_info "部署配置文件..."
    kubectl apply -f ../02-configmaps.yaml
    kubectl apply -f ../03-secrets.yaml
    log_success "配置文件部署完成"
}

# 部署基础设施
deploy_infrastructure() {
    log_info "部署基础设施服务..."
    
    # 部署 Zookeeper
    log_info "部署 Zookeeper..."
    kubectl apply -f ../10-zookeeper.yaml
    kubectl wait --for=condition=ready pod -l app=zookeeper -n cv-system --timeout=300s
    log_success "Zookeeper 部署完成"
    
    # 部署 Kafka
    log_info "部署 Kafka..."
    kubectl apply -f ../11-kafka.yaml
    kubectl wait --for=condition=ready pod -l app=kafka -n cv-system --timeout=300s
    log_success "Kafka 部署完成"
    
    # 部署 MongoDB
    log_info "部署 MongoDB..."
    kubectl apply -f ../12-mongodb.yaml
    kubectl wait --for=condition=ready pod -l app=mongodb -n cv-system --timeout=300s
    log_success "MongoDB 部署完成"
}

# 部署应用服务
deploy_applications() {
    log_info "部署应用服务..."
    
    # 部署 Scheduler
    log_info "部署 Scheduler..."
    kubectl apply -f ../20-scheduler.yaml
    kubectl wait --for=condition=ready pod -l app=scheduler -n cv-system --timeout=300s
    log_success "Scheduler 部署完成"
    
    # 部署 RTSP Server
    log_info "部署 RTSP Server..."
    kubectl apply -f ../22-rtsp-server.yaml
    kubectl wait --for=condition=ready pod -l app=rtsp-server -n cv-system --timeout=300s
    log_success "RTSP Server 部署完成"
    
    # 部署 Inference (可选，需要 GPU 节点)
    if kubectl get nodes -l accelerator=nvidia-tesla-a10 &> /dev/null; then
        log_info "部署 Inference..."
        kubectl apply -f ../21-inference.yaml
        kubectl wait --for=condition=ready pod -l app=inference -n cv-system --timeout=600s
        log_success "Inference 部署完成"
    else
        log_warning "未找到 GPU 节点，跳过 Inference 部署"
    fi
    
    # 部署 Inference Mock
    log_info "部署 Inference Mock..."
    kubectl apply -f ../23-inference-mock.yaml
    kubectl wait --for=condition=ready pod -l app=inference-mock -n cv-system --timeout=300s
    log_success "Inference Mock 部署完成"
}

# 部署网络配置
deploy_networking() {
    log_info "部署网络配置..."
    
    # 检查是否有 Ingress Controller
    if kubectl get ingressclass nginx &> /dev/null; then
        kubectl apply -f ../30-ingress.yaml
        log_success "Ingress 配置部署完成"
    else
        log_warning "未找到 NGINX Ingress Controller，跳过 Ingress 部署"
    fi
}

# 部署监控
deploy_monitoring() {
    log_info "部署监控配置..."
    
    # 检查是否有 Prometheus Operator
    if kubectl get crd servicemonitors.monitoring.coreos.com &> /dev/null; then
        kubectl apply -f ../40-monitoring.yaml
        log_success "监控配置部署完成"
    else
        log_warning "未找到 Prometheus Operator，跳过监控部署"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    echo
    log_info "=== Pod 状态 ==="
    kubectl get pods -n cv-system -o wide
    
    echo
    log_info "=== Service 状态 ==="
    kubectl get services -n cv-system
    
    echo
    log_info "=== PVC 状态 ==="
    kubectl get pvc -n cv-system
    
    echo
    log_info "=== Ingress 状态 ==="
    kubectl get ingress -n cv-system 2>/dev/null || log_warning "未部署 Ingress"
    
    # 健康检查
    log_info "执行健康检查..."
    
    # 检查 Scheduler
    if kubectl get pod -l app=scheduler -n cv-system | grep -q Running; then
        if kubectl exec -n cv-system deployment/scheduler -- curl -f http://localhost:8080/actuator/health &> /dev/null; then
            log_success "Scheduler 健康检查通过"
        else
            log_warning "Scheduler 健康检查失败"
        fi
    fi
    
    # 检查 Inference Mock
    if kubectl get pod -l app=inference-mock -n cv-system | grep -q Running; then
        if kubectl exec -n cv-system deployment/inference-mock -- curl -f http://localhost:8081/health &> /dev/null; then
            log_success "Inference Mock 健康检查通过"
        else
            log_warning "Inference Mock 健康检查失败"
        fi
    fi
    
    log_success "部署验证完成"
}

# 显示访问信息
show_access_info() {
    log_info "=== 访问信息 ==="
    
    # 获取 NodePort 信息
    local scheduler_nodeport=$(kubectl get svc scheduler -n cv-system -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "N/A")
    local rtsp_nodeport=$(kubectl get svc rtsp-server-external -n cv-system -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "30554")
    
    # 获取节点 IP
    local node_ip=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="ExternalIP")].address}' 2>/dev/null)
    if [ -z "$node_ip" ]; then
        node_ip=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
    fi
    
    echo
    echo "🌐 服务访问地址:"
    echo "  Scheduler API: http://${node_ip}:${scheduler_nodeport}"
    echo "  RTSP Stream:   rtsp://${node_ip}:${rtsp_nodeport}/knight"
    echo
    echo "📋 kubectl 命令:"
    echo "  查看 Pod:      kubectl get pods -n cv-system"
    echo "  查看日志:      kubectl logs -f deployment/scheduler -n cv-system"
    echo "  进入容器:      kubectl exec -it deployment/scheduler -n cv-system -- bash"
    echo
    echo "🔧 端口转发 (本地访问):"
    echo "  Scheduler:     kubectl port-forward svc/scheduler 8080:8080 -n cv-system"
    echo "  Inference:     kubectl port-forward svc/inference 9001:9001 -n cv-system"
    echo "  Inference Mock: kubectl port-forward svc/inference-mock 8081:8081 -n cv-system"
    echo
}

# 主函数
main() {
    echo "🚀 开始部署 CV 推理系统到 Kubernetes"
    echo "========================================"
    
    # 解析参数
    SKIP_IMAGES=false
    SKIP_MONITORING=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-images)
                SKIP_IMAGES=true
                shift
                ;;
            --skip-monitoring)
                SKIP_MONITORING=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-images      跳过镜像检查"
                echo "  --skip-monitoring  跳过监控部署"
                echo "  -h, --help         显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_dependencies
    
    if [ "$SKIP_IMAGES" = false ]; then
        check_images
    fi
    
    create_namespace
    deploy_storage
    deploy_configs
    deploy_infrastructure
    deploy_applications
    deploy_networking
    
    if [ "$SKIP_MONITORING" = false ]; then
        deploy_monitoring
    fi
    
    verify_deployment
    show_access_info
    
    echo
    log_success "🎉 CV 推理系统部署完成！"
    echo "========================================"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
