#!/bin/bash

# 验证 K8s YAML 文件序号和内容的脚本
# 作者: CV System Team
# 版本: v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 进入 k8s 目录
cd "$(dirname "$0")/.."

echo "🔍 验证 K8s YAML 文件"
echo "===================="

# 检查文件是否存在
log_info "检查文件存在性..."

expected_files=(
    "01-namespace.yaml"
    "02-storage.yaml"
    "03-configmaps.yaml"
    "04-secrets.yaml"
    "05-zookeeper.yaml"
    "06-kafka.yaml"
    "07-mongodb.yaml"
    "08-scheduler.yaml"
    "09-inference.yaml"
    "10-rtsp-server.yaml"
    "11-inference-mock.yaml"
    "12-ingress.yaml"
    "13-monitoring.yaml"
)

missing_files=()
existing_files=()

for file in "${expected_files[@]}"; do
    if [ -f "$file" ]; then
        existing_files+=("$file")
        log_success "✅ $file"
    else
        missing_files+=("$file")
        log_error "❌ $file (缺失)"
    fi
done

echo
log_info "文件统计:"
echo "  存在: ${#existing_files[@]}"
echo "  缺失: ${#missing_files[@]}"

# 检查旧文件是否还存在
echo
log_info "检查旧文件..."

old_files=(
    "00-namespace.yaml"
    "20-scheduler.yaml"
    "21-inference.yaml"
    "22-rtsp-server.yaml"
    "23-inference-mock.yaml"
    "30-ingress.yaml"
    "40-monitoring.yaml"
)

old_files_found=()

for file in "${old_files[@]}"; do
    if [ -f "$file" ]; then
        old_files_found+=("$file")
        log_warning "⚠️  $file (旧文件，建议删除)"
    fi
done

if [ ${#old_files_found[@]} -eq 0 ]; then
    log_success "✅ 没有发现旧文件"
else
    echo
    log_warning "发现 ${#old_files_found[@]} 个旧文件，建议删除:"
    for file in "${old_files_found[@]}"; do
        echo "  rm $file"
    done
fi

# 验证 YAML 语法
echo
log_info "验证 YAML 语法..."

yaml_errors=0

for file in "${existing_files[@]}"; do
    if command -v yamllint &> /dev/null; then
        if yamllint "$file" &> /dev/null; then
            log_success "✅ $file (语法正确)"
        else
            log_error "❌ $file (语法错误)"
            yaml_errors=$((yaml_errors + 1))
        fi
    elif python3 -c "import yaml" &> /dev/null; then
        if python3 -c "import yaml; yaml.safe_load_all(open('$file'))" &> /dev/null; then
            log_success "✅ $file (语法正确)"
        else
            log_error "❌ $file (语法错误)"
            yaml_errors=$((yaml_errors + 1))
        fi
    else
        log_warning "⚠️  无法验证 $file (缺少 yamllint 或 python3-yaml)"
    fi
done

# 检查脚本中的文件引用
echo
log_info "检查脚本中的文件引用..."

script_files=(
    "scripts/deploy.sh"
    "scripts/cleanup.sh"
    "Makefile"
)

reference_errors=0

for script in "${script_files[@]}"; do
    if [ -f "$script" ]; then
        log_info "检查 $script..."
        
        # 检查是否还有旧的文件引用
        old_refs=$(grep -E "(00|20|21|22|23|30|40)-.*\.yaml" "$script" | wc -l)
        if [ "$old_refs" -gt 0 ]; then
            log_error "❌ $script 中发现 $old_refs 个旧文件引用"
            grep -n -E "(00|20|21|22|23|30|40)-.*\.yaml" "$script" || true
            reference_errors=$((reference_errors + 1))
        else
            log_success "✅ $script (文件引用已更新)"
        fi
    else
        log_warning "⚠️  脚本文件不存在: $script"
    fi
done

# 检查部署顺序
echo
log_info "验证部署顺序..."

log_info "当前文件顺序:"
ls -1 [0-9][0-9]-*.yaml 2>/dev/null | while read -r file; do
    echo "  $file"
done

# 生成总结报告
echo
echo "📊 验证报告"
echo "============"

total_errors=0

if [ ${#missing_files[@]} -gt 0 ]; then
    log_error "缺失文件: ${#missing_files[@]} 个"
    total_errors=$((total_errors + ${#missing_files[@]}))
fi

if [ ${#old_files_found[@]} -gt 0 ]; then
    log_warning "旧文件: ${#old_files_found[@]} 个 (建议清理)"
fi

if [ $yaml_errors -gt 0 ]; then
    log_error "YAML 语法错误: $yaml_errors 个"
    total_errors=$((total_errors + yaml_errors))
fi

if [ $reference_errors -gt 0 ]; then
    log_error "脚本引用错误: $reference_errors 个"
    total_errors=$((total_errors + reference_errors))
fi

echo
if [ $total_errors -eq 0 ]; then
    log_success "🎉 所有检查通过！文件序号和内容都正确。"
    echo
    echo "可以开始部署:"
    echo "  make deploy    # 或"
    echo "  ./scripts/deploy.sh"
    exit 0
else
    log_error "❌ 发现 $total_errors 个问题，请修复后再部署。"
    echo
    echo "修复建议:"
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo "1. 创建缺失的文件或从备份恢复"
    fi
    
    if [ ${#old_files_found[@]} -gt 0 ]; then
        echo "2. 删除旧文件: rm ${old_files_found[*]}"
    fi
    
    if [ $yaml_errors -gt 0 ]; then
        echo "3. 修复 YAML 语法错误"
    fi
    
    if [ $reference_errors -gt 0 ]; then
        echo "4. 更新脚本中的文件引用"
    fi
    
    exit 1
fi
